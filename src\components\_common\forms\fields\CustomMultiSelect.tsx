import { useInfoFieldPopper } from '@/hooks/useInfoFieldPopper';
import { useStatusFieldPopper } from '@/hooks/useStatusFieldPopper';
import { OptionItem, TValues } from '@/interfaces/_common';
import { PreApprovedParametersGroup } from '@/interfaces/parameters';
import { useFieldMetadataByFieldNameFormState, useFormStore } from '@/stores/formStore';
import { ErrorMessage } from '@hookform/error-message';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import { InputAdornment, SxProps, Theme } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import Checkbox from '@mui/material/Checkbox';
import Chip from '@mui/material/Chip';
import TextField from '@mui/material/TextField';
import { useEffect } from 'react';
import { Controller, get, useFormContext } from 'react-hook-form';
import FormValidationMessage from '../FormValidationMessage';
import InfoFieldIcon from '../InfoFieldIcon';
import InfoFieldPopper from '../InfoFieldPopper';
import StatusFieldIcon from '../StatusFieldIcon';
import StatusFieldPopper from '../StatusFieldPopper';

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

interface CustomMultiSelectProps<TData extends TValues> {
    id: string;
    name: keyof TData;
    label: string;
    options: OptionItem[];
    groupId: string;
    optionSelector?: keyof OptionItem;
    placeholder?: string;
    shouldUnregister?: boolean;
    required?: boolean;
    sx?: SxProps<Theme>;
    info?: string;
}
const CustomMultiSelect = <TData extends TValues>(props: CustomMultiSelectProps<TData>) => {
    const {
        id,
        name,
        label,
        options,
        optionSelector = "label",
        placeholder,
        shouldUnregister,
        groupId,
        sx,
        info
    } = props;
    const { formState: { errors }, getValues } = useFormContext();

    const grupos = getValues("grupos") as PreApprovedParametersGroup[];
    const index = grupos.findIndex((grupo) => grupo.id === groupId);
    const name_ = `grupos.${index}.parameters.${name as string}`;
    const error_ = get(errors, name_);

    const {
        handleClickInfo,
        anchorEl,
        open,
        id: idPopper,
        handleClickAway
    } = useInfoFieldPopper(name_);

    const {
        handleClickInfo: handleClickStatus,
        anchorEl: anchorElStatus,
        open: openStatus,
        id: idStatus,
        handleClickAway: handleClickAwayStatus
    } = useStatusFieldPopper(name as string + "status");

    const addError = useFormStore((state) => state.addError);
    const removeErrors = useFormStore((state) => state.removeError);
    const fieldMetadata = useFieldMetadataByFieldNameFormState(name as string);
    const fieldMetadataStatus = fieldMetadata?.state ?? "approved";
    useEffect(() => {
        const key = name_ as string;
        if (error_) {
            const message = error_.message as string ?? "";
            addError({ groupId, field: key, message });
        } else {
            removeErrors({ groupId, field: key });
        }
    }, [errors]);

    return (<>
        <Controller
            name={name_}
            shouldUnregister={shouldUnregister}
            render={({ field, formState }) => {
                const values_ = field.value as TData[] ?? [];
                return (
                    <Autocomplete
                        sx={{
                            ...sx,
                            '& .MuiAutocomplete-clearIndicator': {
                                visibility: values_.length > 0 ? 'visible' : 'hidden',
                                display: values_.length > 0 ? "inline-flex" : "none"
                            }
                        }}
                        disabled={fieldMetadataStatus == "pending"}
                        multiple
                        id={id}
                        options={options}
                        disableCloseOnSelect
                        getOptionLabel={(option) => typeof option === 'string' ? option : option.label}
                        isOptionEqualToValue={(option, value) => option.value === value}
                        renderOption={(props, option, { selected }) => (
                            <li {...props} key={option.value}>
                                <Checkbox
                                    icon={icon}
                                    checkedIcon={checkedIcon}
                                    style={{ marginRight: 8 }}
                                    checked={selected}
                                />
                                {option[optionSelector]}
                            </li>
                        )}
                        renderTags={(tagValue, getTagProps, { options }) => {
                            return tagValue.map((option, index) => (
                                <Chip
                                    {...getTagProps({ index })}
                                    key={option}
                                    label={
                                        options.find(option_ => option_.value === option)?.label
                                    }
                                />
                            ))
                        }}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                label={label}
                                placeholder={placeholder}
                                error={error_ ? true : false}
                                helperText={
                                    <ErrorMessage
                                        name={name_}
                                        errors={errors}
                                        render={({ message }) => <FormValidationMessage message={message} />}
                                    />
                                }

                                InputProps={{
                                    ...params.InputProps,
                                    endAdornment: info && !fieldMetadata ? (
                                        <>
                                            {params.InputProps.endAdornment}
                                            <InputAdornment position="start" sx={{ position: "absolute", right: "61px" }}>
                                                <InfoFieldIcon onClick={handleClickInfo} />
                                            </InputAdornment>
                                        </>
                                    ) : (
                                        <>
                                            {params.InputProps.endAdornment}
                                            <InputAdornment position="end" sx={{ position: "absolute", right: "61px" }}>
                                                {info && <InfoFieldIcon onClick={handleClickInfo} />}
                                                {fieldMetadata && <StatusFieldIcon onClick={handleClickStatus} status={fieldMetadata.state} />}
                                            </InputAdornment>
                                        </>
                                    )
                                }}

                                sx={{
                                    width: "100%",
                                    "& .MuiOutlinedInput-root": {
                                        "& fieldset": {
                                            borderColor: get(formState.dirtyFields, name_) ? "orange" : "rgba(0, 0, 0, 0.23)",
                                        }
                                    },
                                    "& .MuiDisabled": {
                                        "& fieldset": {
                                            borderColor: get(formState.dirtyFields, name_) ? "orange" : "rgba(0, 0, 0, 0.23)",
                                        }
                                    }
                                }}
                            />
                        )}
                        onChange={(event, value, reason, details) => {
                            const newValue = value.map((item: any) => {
                                const value_ = typeof item === 'string' ? item : item.value;
                                return value_;
                            })
                            field.onChange(newValue);
                        }}
                        value={field.value ?? []}
                    />
                )
            }}

        />
        <ErrorMessage
            name={name_}
            errors={errors}
            render={({ message }) => <FormValidationMessage message={message} />}
        />
        {info && (
            <InfoFieldPopper
                id={id}
                open={open}
                anchorEl={anchorEl}
                info={info}
                handleClickAway={handleClickAway}
            />
        )}

        {fieldMetadata && (
            <StatusFieldPopper
                id={idStatus}
                status={fieldMetadata.state}
                open={openStatus}
                anchorEl={anchorElStatus}
                handleClickAway={handleClickAwayStatus}
            />
        )}
    </>
    )
}

export default CustomMultiSelect