import { FilterControlsItem, EFilterControlsTypes, ESelectFilterControlType } from "@/interfaces/filterControls";

export const parametersFilters: FilterControlsItem[] = [
    {
        key: "id",
        label: "Cod. Cliente",
        type: EFilterControlsTypes.TextField,
        control: []
    },
    {
        key: "asd",
        label: "Cod. Cuenta",
        type: EFilterControlsTypes.TextField,
        control: []
    },
    {
        key: "b",
        label: "Monto Prestamo Max",
        type: EFilterControlsTypes.TextField,
        control: []
    },
    {
        key: "c",
        label: "Edad",
        type: EFilterControlsTypes.TextField,
        control: []
    },
    {
        key: "d",
        label: "Campaña",
        type: EFilterControlsTypes.TextField,
        control: []
    },
    {
        key: "d",
        label: "Antiguedad",
        type: EFilterControlsTypes.TextField,
        control: []
    },
    {
        key: "d",
        label: "Calif. Conti",
        type: EFilterControlsTypes.TextField,
        control: []
    },
    {
        key: "d",
        label: "Calif. BCP",
        type: EFilterControlsTypes.TextField,
        control: []
    },
    {
        key: "d",
        label: "Atraso",
        type: EFilterControlsTypes.TextField,
        control: []
    },
    {
        key: "d",
        label: "Codeudor",
        type: EFilterControlsTypes.TextField,
        control: []
    },
    {
        key: "d",
        label: "Mora",
        type: EFilterControlsTypes.TextField,
        control: []
    },
    {
        key: "d",
        label: "Sucursal",
        type: EFilterControlsTypes.TextField,
        control: []
    },
    {
        key: "d",
        label: "Lista Negativa",
        type: EFilterControlsTypes.TextField,
        control: []
    },
    {
        key: "d",
        label: "ROS",
        type: EFilterControlsTypes.TextField,
        control: []
    },
    {
        key: "d",
        label: "Cant. Prestamo",
        type: EFilterControlsTypes.TextField,
        control: []
    },
];