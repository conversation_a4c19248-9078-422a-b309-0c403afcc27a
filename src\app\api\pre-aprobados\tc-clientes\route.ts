import { baseApi, preAprobadosApi } from "@/lib/api";
import { TCClientsModel } from "@/models/responses/clientServices";
import { preApprovedService } from "@/services/preApprovedService";
import axios from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    client?: TCClientsModel[],
    error?: string
    status: number
    meta?: any
}
export async function GET(req: NextRequest) {
    let responseBody: Response = {
        client: undefined,
        error: undefined,
        status: 200
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 401;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    preAprobadosApi.defaults.nextRequest = req;

    const searchParams = req.nextUrl.searchParams
    const month = searchParams.get('month');
    const year = searchParams.get('year');
    try {
        const resp = await preApprovedService.getPreApprovedTc(month, year);
        responseBody.client = resp.data;
        return NextResponse.json(responseBody.client, { status: 200 });
    } catch (error: any) {
        responseBody.error = "Error al obtener los datos del cliente";
        responseBody.status = 500;
        responseBody.meta = error;
        if (axios.isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
            responseBody.meta = {
                data: error.config?.data,
                url: error.config?.url,
            };
        }
        console.error(error);
        return NextResponse.json(responseBody, { status: responseBody.status });
    }

}