import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import { Button, CircularProgress } from '@mui/material';
import Box from "@mui/material/Box";
import IconButton from "@mui/material/IconButton";
import { closeSnackbar, enqueueSnackbar } from "notistack";

export type TypeError = "error" | "success" | "warning" | "info"

const customNotify = (type: TypeError, message: string) => {
    enqueueSnackbar(message, { variant: type });
}
const delay = 5000;
const errorNotify = (message: string) => {

    enqueueSnackbar(message,
        {
            variant: "error",
            action: (key) => (
                <IconButton onClick={() => closeSnackbar(key)}>
                    <CloseIcon />
                </IconButton>
            ),
            autoHideDuration: delay,
            style: { whiteSpace: 'pre-line', fontSize: '18px', fontWeight: 'bold', padding: '12px' }
        });
}

const loader = (message: string) => {
    enqueueSnackbar(message, {
        variant: 'info',
        style: { whiteSpace: 'pre-line', fontSize: '18px', fontWeight: 'bold', padding: '12px' },
        persist: true,
        hideIconVariant: true,
        action: () => (
            <CircularProgress size={24} sx={{ marginLeft: 1, color: 'white' }} />
        )
    });
};

const successNotify = (message: string) => {
    enqueueSnackbar(message,
        {
            variant: "success",
            action: (key) => (
                <IconButton onClick={() => closeSnackbar(key)}>
                    <CloseIcon />
                </IconButton>
            ),
            autoHideDuration: delay,
            style: { whiteSpace: 'pre-line', fontSize: '18px', fontWeight: 'bold', padding: '12px' }
        });
}

const warningNotify = (message: string) => {
    enqueueSnackbar(message, 
        { 
            variant: "warning",
            autoHideDuration: delay,
            action: (key) => (
                <IconButton onClick={() => closeSnackbar(key)}>
                    <CloseIcon />
                </IconButton>
            ),
            style: { whiteSpace: 'pre-line', color: 'black', fontSize: '18px', fontWeight: 'bold', padding: '20px', backgroundColor: '#FFCD3D' } 
        });
}

const infoNotify = (message: string) => {
    enqueueSnackbar(message, { autoHideDuration: delay, variant: "info", style: { whiteSpace: 'pre-line', fontSize: '18px', fontWeight: 'bold', padding: '12px' } });
}

interface ConfirmationProps {
    message: string;
    onConfirm: (snackbarId: string | number) => void;
    onCancel?: (snackbarId: string | number) => void;
    confirmText?: string;
    cancelText?: string;
}

const confirmationAction = ({ message, onConfirm, onCancel = (snackbarId) => closeSnackbar(snackbarId), confirmText = "Aceptar", cancelText = "Cancelar" }: ConfirmationProps) => {
    const action = (snackbarId: string | number) => (
        <Box>
            <Button variant="contained" color="inherit" onClick={() => {
                onConfirm(snackbarId);
            }} startIcon={<CheckIcon  fontSize="small" color='success'/>} sx={{ margin: 1, color: 'black' }}>
                <strong>CONFIRMAR</strong>
            </Button>
            <Button variant="contained" color="inherit" onClick={() => {
                onCancel(snackbarId);
            }} startIcon={<CloseIcon  fontSize="small" color='error'/>} sx={{ margin: 1, color: 'black' }}>
                <strong>CANCELAR</strong>
            </Button>
        </Box>
    );

    enqueueSnackbar(message, {
        action,
        variant: "warning",
        persist: true,
        style: { whiteSpace: 'pre-line', color: 'white', fontSize: '18px', fontWeight: 'bold', padding: '25px', backgroundColor: '#195AA7' }
    })
}

const notify = {
    loader: loader,
    error: errorNotify,
    success: successNotify,
    warning: warningNotify,
    info: infoNotify,
    custom: customNotify,
    confirmation: confirmationAction
};

export default notify;