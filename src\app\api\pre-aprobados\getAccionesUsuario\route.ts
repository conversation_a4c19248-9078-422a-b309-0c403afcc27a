import { utilHelper } from "@/helpers/utilHelper";
import { preAprobadosApi } from "@/lib/api";
import { usuarioServices } from "@/services/usuarioServices";
import axios from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    data?: {},
    error?: string
    status: number
    meta?: any
}
export async function GET(req: NextRequest) {
    let responseBody: Response = {
        data: undefined,
        error: undefined,
        status: 200
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 401;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    preAprobadosApi.defaults.nextRequest = req;

    const searchParams = req.nextUrl.searchParams
    const codigoUsuario = searchParams.get('codigoUsuario');

    try {
        if (utilHelper.esValido(codigoUsuario)) {
            const resp = await usuarioServices.getAccionesUsuario(codigoUsuario);
            responseBody.data = resp?.data;
            return NextResponse.json(responseBody.data, { status: 200 });
        } else {
            responseBody.data = {};
            responseBody.error = "El codigo del usuario no es válido";
            responseBody.status = 400;
            return NextResponse.json(responseBody, { status: responseBody.status });
        }
    } catch (error: any) {
        console.error(error);
        responseBody.error = "Error al obtener las posibles acciones del usuario en el modulo Pre Aprobados.";
        responseBody.status = 500;
        if (axios.isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
            responseBody.meta = {
                data: error.config?.data,
                url: error.config?.url,
                test: process.env.NEXT_PUBLIC_BASE_API_URL
            };
        }
        return NextResponse.json(responseBody, { status: responseBody.status });
    }
}