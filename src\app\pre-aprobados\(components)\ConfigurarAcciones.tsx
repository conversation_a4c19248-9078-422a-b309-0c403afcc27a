'use client'
import React, { useEffect } from 'react';
import {
    Box,
    CircularProgress,
    Divider,
    IconButton,
    InputBase,
    Paper,
    Typography,
    Grid,
    Button,
    Stack
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import Chip from '@mui/material/Chip';
import SettingsIcon from '@mui/icons-material/Settings';
import ModalGestionAcciones from '@/app/pre-aprobados/(modals)/ModalGestionAcciones';
import '../(styles)/configurarRolesParametros.css';
import UseConfigurarAcciones from '../(hooks)/UseConfigurarAcciones';
import DatosUsuario from './DatosUsuario';
import { utilHelper } from '@/helpers/utilHelper';

const ConfigurarAcciones = () => {
    const {
        datosUsuario,
        mostrarDatosUsuario,
        esperandoRespuesta,
        openModal,
        rolesUsuario,
        rolesParametros,
        selectedRoles,
        cargandoRoles,
        handleBuscarClick,
        handleKeyPress,
        handleOpenModal,
        handleCloseModal,
        handleChangeSelectedRoles,
        handleGuardarCambios,
        setOriginalRoles,
        setUsuarioDominio,
        obtenerAcciones
    } = UseConfigurarAcciones();

    useEffect(() => {
        const fetchData = async () => {
            await obtenerAcciones();
            setOriginalRoles(selectedRoles);
        };
        fetchData();
    }, [openModal]);

    return (
        <Stack spacing={2}>
            <Paper variant="outlined" square={false} className="container-principal">
                <Typography variant="inherit" style={{ fontWeight: 'bold', fontSize: '15px', marginBottom: '4px' }}>Buscar usuario por nombre de dominio</Typography>
                <Paper component="form" sx={{ p: '2px 4px', display: 'flex', alignItems: 'center', width: 400, border: '1px solid #c7c7c7' }}>
                    <InputBase sx={{ ml: 1, flex: 1 }} placeholder="Ejemplo: juan.perez" inputProps={{ 'aria-label': 'buscar usuario' }} onChange={(e) => setUsuarioDominio(e.target.value)} onKeyDown={handleKeyPress} />
                    <Divider sx={{ height: 28, m: 0.5 }} orientation="vertical" />
                    <IconButton type="button" sx={{ p: '10px' }} aria-label="search" onClick={handleBuscarClick}>
                        <SearchIcon />
                    </IconButton>
                </Paper>

                {esperandoRespuesta && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
                        <CircularProgress />
                    </Box>
                )}

                {mostrarDatosUsuario && datosUsuario && !esperandoRespuesta && (
                    <Box>
                        <DatosUsuario {...datosUsuario} />
                        <Paper variant="outlined" square={false} className="container-datos-usuario">
                            <Typography variant="inherit" style={{ fontWeight: 'bold', fontSize: '15px' }}>ACCIONES ASIGNADAS</Typography>
                            <Divider orientation="horizontal" sx={{ marginTop: '8px', marginBottom: '20px' }} />

                            {cargandoRoles ? (
                                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                                    <CircularProgress />
                                    <span style={{ fontWeight: 'bold', marginLeft: '15px' }}>
                                        Cargando acciones del usuario...
                                    </span>
                                </Box>
                            ) : null }

                            {!cargandoRoles && utilHelper.esValido(rolesUsuario) ? (
                                <Grid container spacing={1}>
                                    {Array.isArray(rolesUsuario) && rolesUsuario.some(r => r.estado == "A") ? (
                                        rolesUsuario.map((rol) => (
                                            rol.estado === "A" && (
                                                <Grid item key={rol.idRol}>
                                                    <Chip label={rolesParametros.find(p => p.idRol == rol.idRol)?.descripcion} />
                                                </Grid>
                                            )
                                        ))
                                    ) : (
                                        <Typography variant="inherit" style={{ fontSize: '15px', marginTop: '10px', marginLeft: '10px' }}>
                                            No se encontraron acciones asignadas al usuario
                                        </Typography>
                                    )}
                                </Grid>
                            ) : null }

                            {!cargandoRoles && !utilHelper.esValido(rolesUsuario) ? (
                                <Typography variant="inherit" style={{ fontSize: '15px', marginTop: '10px', marginLeft: '10px' }}>
                                    No se encontraron acciones asignadas al usuario
                                </Typography>
                            ) : null }

                            <Box sx={{ display: 'flex', justifyContent: 'center', marginTop: '25px' }}>
                                <Button variant="contained" size="small" startIcon={<SettingsIcon />} onClick={handleOpenModal}>
                                    Gestionar Acciones
                                </Button>
                            </Box>
                        </Paper>
                    </Box>
                )}

                <ModalGestionAcciones
                    open={openModal}
                    handleClose={handleCloseModal}
                    handleGuardarCambios={handleGuardarCambios}
                    rolesParametros={rolesParametros}
                    selectedRoles={selectedRoles}
                    handleChangeSelectedRoles={handleChangeSelectedRoles}
                />
            </Paper>
        </Stack>
    );
}

export default ConfigurarAcciones;