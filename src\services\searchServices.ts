import { SearchResponse } from "@/interfaces/_common";
import { SucursalModel } from "@/models/responses/authServices";
import { ClientModel } from "@/models/responses/clientServices";
import { clientServices } from "./clientServices";
import { commonServices } from "./commonServices";

const companyByRuc = async (ruc: string): Promise<SearchResponse<ClientModel>> => {
    let data = undefined;
    try {
        const resp = await clientServices.getInternalDataClientByDocNumber(ruc);
        data = resp.data;
    } catch (error) {
        console.error(error);
    }

    const nombres = `${data?.primerNombre ?? ""} ${data?.segundoNombre ?? ""}`;
    const apellidos = `${data?.primerApellido ?? ""} ${data?.segundoApellido ?? ""}`;
    const nombreCompleto = `${nombres} ${apellidos}`;
    return {
        items: {
            label: nombreCompleto,
            value: data?.codigoCliente ?? "",
        },
        data: data,
    };
}

const companyByCode = async (code: string): Promise<SearchResponse<ClientModel>> => {
    let data = undefined;
    try {
        const resp = await clientServices.getInternalDataClientByCode(code);
        data = resp.data;
    } catch (error) {
        console.error(error);
    }

    const nombres = `${data?.primerNombre ?? ""} ${data?.segundoNombre ?? ""}`;
    const apellidos = `${data?.primerApellido ?? ""} ${data?.segundoApellido ?? ""}`;
    const nombreCompleto = `${nombres} ${apellidos}`;
    return {
        items: {
            label: nombreCompleto,
            value: data?.codigoCliente ?? "",
        },
        data: data,
    };
}

const branches = async (): Promise<SearchResponse<SucursalModel>> => {
    let data = undefined;
    let items: {
        label: string;
        value: string;
    }[] = [];
    try {
        const resp = await commonServices.getInternalAllBranches();
        data = resp.data;
        items = resp.data.map((item) => {
            return {
                label: item.descripcion,
                value: item.codigo,
            };
        });
    } catch (error) {
        console.error(error);
    }


    return {
        items: items,
        data: data,
    };
}

export const searchServices = {
    companyByRuc,
    companyByCode,
    branches,
}