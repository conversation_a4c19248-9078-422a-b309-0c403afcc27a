"use client"
import { FormGroubTab } from "@/interfaces/forms";
import { useActiveTab, useFormGroupTabStore } from "@/stores/formGroupTabStore";
import Tab from "@mui/material/Tab";
import Tabs from "@mui/material/Tabs";
import styled from '@mui/material/styles/styled';

const TabsStyled = styled(Tabs)(({ theme }) => ({
    "&.MuiTabs-root": {
        width: '100%',
        borderRadius: theme.spacing(1),
        background: "#1D428A",
        padding: 10,
        boxShadow: '0px 3px 15px rgba(34, 35, 58, 0.5)',
    },
    '& .MuiTabs-indicator': {
        height: '100%',
        borderRadius: theme.spacing(1),
        backgroundColor: "rgba(255, 255, 255, .2)",
    },


}));

const TabItemStyled = styled(Tab)(({ theme }) => ({
    "&.MuiTab-root": {
        textTransform: 'initial',
        margin: `0 ${theme.spacing(2)}px`,
        minWidth: 0,
        [theme.breakpoints.up('md')]: {
            minWidth: 0,
        },
    },
    "&.MuiTab-wrapped": {
        fontWeight: 'normal',
        letterSpacing: 0.5,
        color: "white",
    },
    "&.Mui-selected": {
        color: "white",
    },
    "&.MuiButtonBase-root": {
        color: "white",
    },
}));

interface FormGroupTabListProps {
    tabs: FormGroubTab[];
}
const FormGroupTabList = ({ tabs }: FormGroupTabListProps) => {
    const activeTab = useActiveTab();
    const toggleAccordion = useFormGroupTabStore((state) => state.setActiveTab);
    const handleChange = (event: React.SyntheticEvent, newValue: string) => {
        toggleAccordion(newValue);
    }
    return (
        <>
            <TabsStyled
                value={activeTab ?? tabs[0].id}
                onChange={handleChange}
                textColor="primary"
            >
                {
                    tabs.map((tab) => (
                        <TabItemStyled
                            key={tab.id}
                            value={tab.id}
                            label={tab.label}
                        />
                    ))
                }
            </TabsStyled>
        </>
    )
}

export default FormGroupTabList