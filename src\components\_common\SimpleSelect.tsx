import FormControl from '@mui/material/FormControl'
import MenuItem from '@mui/material/MenuItem'
import Select from '@mui/material/Select'
import React from 'react'
import IconSelectButton from './IconSelectButton'

interface SimpleSelectProps {
    label: string;
    options: { value: string, label: string }[];
    value: string;
    onChange: (value: string) => void;
}
const SimpleSelect = ({ label, options, value, onChange }: SimpleSelectProps) => {

    const [open, setOpen] = React.useState(false);
    const handleChange = (value: string) => {
        onChange(value);
    }
    return (
        <FormControl
            variant={"outlined"}
            sx={{
                flexDirection: "row",
                alignItems: "center",
                minWidth: "unset",
            }}
        >
            <IconSelectButton onClick={() => setOpen(true)} />
            <Select
                value={value}
                onChange={(e) => {
                    handleChange(e.target.value as string);
                }}
                open={open}
                onClose={() => setOpen(false)}
                label={label}
                hidden
                sx={{   
                    visibility: "hidden",
                    "&.MuiInputBase-root":{
                        width: 0,
                    }
                }}

                inputProps={{
                    sx: { width: 0 }
                }}

            >
                {
                    options.map(({ value, label }) => (
                        <MenuItem key={value} value={value}>{label}</MenuItem>
                    ))
                }
            </Select>

        </FormControl>
    )
}

export default SimpleSelect