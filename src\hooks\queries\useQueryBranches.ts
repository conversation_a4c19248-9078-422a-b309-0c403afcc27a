import { authHelper } from "@/helpers/authHelper";
import { commonServices } from "@/services/commonServices";
import { useAuthStore } from "@/stores/authStore";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

const useQueryBranches = () => {
    const { tokenHasExpired } = authHelper;
    const auth = useAuthStore(state => state.auth);
    const { status: statusSession } = useSession();
    const {
        isPending,
        isLoading,
        isFetched,
        error,
        data,
        refetch: fetchConsult,
        isRefetching,
        isRefetchError,
    } = useQuery({
        queryKey: ['queryBranches'],
        queryFn: async () => {
            const resp = await commonServices.getInternalAllBranches();
            return resp.data;
        },
        enabled: statusSession == "authenticated" && !!auth?.access_token && !tokenHasExpired(Number(auth?.expires_in ?? 0)),
        retry: false,
        staleTime: 1000 * 60 * 5,
        refetchOnWindowFocus: false,
        refetchInterval: (query) => {
            const { data } = query.state;
            if (data?.length == 0) {
                return 5000
            }
            return false;
        }, 
    });

    return {
        isPending,
        isLoading,
        isFetched,
        error,
        data,
        fetchConsult,
        isRefetching,
        isRefetchError,
    };
}


export {
    useQueryBranches
};

