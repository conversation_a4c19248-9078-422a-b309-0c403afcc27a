'use client'
import { Paper, Typography, Divider, Box, ListItem, ListItemIcon, ListItemText } from "@mui/material";
import PersonIcon from '@mui/icons-material/Person';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import EmailIcon from '@mui/icons-material/Email';
import WorkIcon from '@mui/icons-material/Work';
import LocationOn from '@mui/icons-material/LocationOn';
import { AuthUserModel } from "@/models/responses/authServices";

const DatosUsuario = (datosUsuario: AuthUserModel) => {
    return (
        <Paper variant="outlined" square={false} className="container-datos-usuario">
        <Typography variant="inherit" style={{ fontWeight: 'bold', fontSize: '15px', textAlign: 'center' }}>DATOS DEL USUARIO</Typography>
        <Divider orientation="horizontal" sx={{ marginTop: '12px', marginBottom: '12px' }} />
        <Box sx={{ display: 'flex', flexDirection: 'row', marginBottom: '12px' }}>
            <div style={{ flex: 1 }}>
                <ListItem>
                    <ListItemIcon>
                        <PersonIcon />
                    </ListItemIcon>
                    <ListItemText primary="Nombre:" secondary={datosUsuario.nombre} />
                </ListItem>
                <ListItem>
                    <ListItemIcon>
                        <AccountCircleIcon />
                    </ListItemIcon>
                    <ListItemText primary="Usuario:" secondary={datosUsuario.codigo} />
                </ListItem>
            </div>
            <div style={{ flex: 1 }}>
                <ListItem>
                    <ListItemIcon>
                        <EmailIcon />
                    </ListItemIcon>
                    <ListItemText primary="Correo:" secondary={datosUsuario.correo} />
                </ListItem>
                <ListItem>
                    <ListItemIcon>
                        <WorkIcon />
                    </ListItemIcon>
                    <ListItemText primary="Departamento:" secondary={datosUsuario.departamento?.descripcion} />
                </ListItem>
            </div>
            <div style={{ flex: 1 }}>
                <ListItem>
                    <ListItemIcon>
                        <LocationOn />
                    </ListItemIcon>
                    <ListItemText primary="Sucursal:" secondary={datosUsuario.sucursal?.descripcion} />
                </ListItem>
            </div>
        </Box>
    </Paper>
    )
}

export default DatosUsuario;