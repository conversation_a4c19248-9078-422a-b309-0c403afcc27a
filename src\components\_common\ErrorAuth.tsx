"use client"
import ErrorImage from '@/components/_common/ErrorImage'
import BootstrapDialogTitle from '@/components/_common/Modal/BootstrapDialogTitle'
import { EAbiType } from '@/interfaces/utils'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import React from 'react'

interface ErrorAuthProps {
    message: string
    subMessage?: string
    description?: string
    abiType?: EAbiType
    buttonText?: string
    buttonOnClick: () => void
}
const ErrorAuth = ({
    message,
    subMessage,
    description,
    abiType,
    buttonText = "Aceptar",
    buttonOnClick
}: ErrorAuthProps) => {
    const [open, setOpen] = React.useState(true);

    return (
        <>
            <Dialog
                onClose={() => { }}
                aria-labelledby="customized-dialog-title"
                open={open}
                PaperProps={{
                    style: { borderRadius: 25 }
                }}
                fullWidth
                disableEscapeKeyDown
            >
                <DialogContent sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                }}>
                    <ErrorImage abiType={abiType} />
                    <BootstrapDialogTitle id="customized-dialog-title">
                        <div className="flex justify-center">
                            <span style={{ color: "#1D428A", fontSize: "30px", paddingBottom: "8px" }}>Pre Aprobados</span>
                        </div>
                    </BootstrapDialogTitle>
                    <div className="text-center pt-2 flex justify-center">
                        <div style={{ width: "100%", fontSize: "25px", textAlign: "center" }}>{message}</div>
                        <div style={{ width: "100%", fontSize: "18px", textAlign: "center" }}>{subMessage}</div>
                        <span style={{ width: "100%", fontSize: "18px", textAlign: "center" }}>{description}</span>
                    </div>
                </DialogContent>
                <DialogActions sx={{
                    justifyContent: "center",
                }}>
                    <Button
                        onClick={buttonOnClick}
                        variant='contained'
                    >
                        {buttonText}
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    )
}
export default ErrorAuth