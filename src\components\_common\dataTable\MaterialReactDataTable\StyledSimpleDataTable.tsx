import { Paper, TableCell, tableCellClasses, Table, styled } from "@mui/material";

//PARA EL VARIANTE NORMAL
const StyledPaperTable = styled(Paper)(({ theme, variant }) => ({
}));
const StyledTableCell = styled(TableCell)(({ theme }) => ({}));
const StyledTable = styled(Table)(({ theme }) => ({}));

//PARA EL VARIANTE NESTED
const StyledPaperTableNested = styled(Paper)(({ theme, variant }) => ({
    boxShadow: "none"
}));
const StyledTableCellNested = styled(TableCell)(({ theme }) => ({}));
const StyledTableNested = styled(Table)(({ theme }) => ({}));

export {
    StyledPaperTable,
    StyledTableCell,
    StyledTable,
    StyledPaperTableNested,
    StyledTableCellNested,
    StyledTableNested
};