import { ParameterMetadata } from "@/interfaces/parameters";
import { create } from "zustand";
import { useShallow } from "zustand/react/shallow";

export interface FormState {
    errors: {
        field: string;
        groupId: string;
        message: string;
    }[];
    setErrors: (errors: {
        field: string;
        groupId: string;
        message: string;
    }[]) => void;
    addError: (error: {
        field: string;
        groupId: string;
        message: string;
    }) => void;
    removeError: (error: {
        field: string;
        groupId: string;
    }) => void;
    cleanErrors: () => void;

    fieldsMetadata: ParameterMetadata[];
    setFieldsMetadata: (fieldsMetadata: ParameterMetadata[]) => void;
    addFieldMetadata: (fieldMetadata: ParameterMetadata) => void;
    removeFieldMetadata: (fieldMetadata: ParameterMetadata) => void;
};

export const useFormStore = create<FormState>((set) => ({
    errors: [],
    setErrors: (errors: {
        field: string;
        groupId: string;
        message: string;
    }[]) => set(
        (state) => ({ ...state, errors })
    ),
    addError: (error: {
        field: string;
        groupId: string;
        message: string;
    }) => set(
        (state) => ({ ...state, errors: [...state.errors, error] })
    ),
    removeError: (error: {
        field: string;
        groupId: string;
    }) => set(
        (state) => ({ ...state, errors: state.errors.filter((e) => e.field !== error.field || e.groupId !== error.groupId) })
    ),
    cleanErrors: () => set(
        (state) => ({ ...state, errors: [] })
    ),
    fieldsMetadata: [],
    setFieldsMetadata: (fieldsMetadata: ParameterMetadata[]) => set(
        (state) => ({ ...state, fieldsMetadata })
    ),
    addFieldMetadata: (fieldMetadata: ParameterMetadata) => set(
        (state) => ({ ...state, fieldsMetadata: [...state.fieldsMetadata, fieldMetadata] })
    ),
    removeFieldMetadata: (fieldMetadata: ParameterMetadata) => set(
        (state) => ({ ...state, fieldsMetadata: state.fieldsMetadata.filter((e) => e.fieldName !== fieldMetadata.fieldName || e.groupId !== fieldMetadata.groupId) })
    ),
}));

export const useErrorsFormState = () => {
    const errors = useFormStore(useShallow((state) => {
        return state.errors;
    }));
    return errors;
}

export const useFieldsMetadataFormState = (id?: string) => {
    const fieldsMetadata = useFormStore(useShallow((state) => {
        return state.fieldsMetadata;
    }));
    return fieldsMetadata;
}

export const useFieldMetadataByFieldNameFormState = (fieldName: string) => {
    const fieldsMetadata = useFormStore(useShallow((state) => {
        return state.fieldsMetadata;
    }));
    return fieldsMetadata.find((e) => e.fieldName === fieldName);
};