# el archivo Values sirve para declarar las variables con valores por defecto y que son utilizadas por las plantillas, los datos son solo de referencia
releaseName: front-finansysweb-pre-aprobados

config:
  NEXT_PUBLIC_VALID_HOST: 'a'
  NEXT_PUBLIC_BASE_API_URL: 'a'
  NEXT_PUBLIC_URL_REDIRECT: 'a'
  NEXT_PUBLIC_API_TIME_OUT: 'a'
  NEXT_PUBLIC_KEY_AES: 'a'
  NEXT_PUBLIC_KEYCLOAK_ID: 'a'
  NEXT_PUBLIC_KEYCLOAK_SECRET: 'a'
  NEXT_PUBLIC_SUBSCRIPTION_KEY: 'a'
  NEXT_PUBLIC_KEYCLOAK_GRANT_TYPE: 'a'
  NEXT_PUBLIC_KEYCLOAK_ISSUER: 'a'
  NEXTAUTH_URL: 'a'
  NEXTAUTH_SECRET: 'a'

deployment:
  replicas: 1

container:
  registry: localhost:5000
  imagePullPolicy: Always

ingress:
  tlsSecret: comodin-2024
  host: finansys-preaprobados.bancontinental.com.py
  create: true
