import { baseApi } from "@/lib/api";
import { SucursalModel } from "@/models/responses/authServices";
import { commonServices } from "@/services/commonServices";
import axios from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    branches?: SucursalModel[],
    error?: string
    status: number
    meta?: any
}
export async function GET(req: NextRequest, res: NextResponse) {
    let responseBody: Response = {
        branches: undefined,
        error: undefined,
        status: 200,
        meta: undefined
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 404;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    baseApi.defaults.nextRequest = req;

    try {
        const resp = await commonServices.getAllBranches();
        responseBody.branches = resp.data;
        return NextResponse.json(responseBody.branches, { status: 200 });
    } catch (error: any) {
        console.error(error);
        responseBody.error = "Error al obtener los datos del cliente";
        responseBody.status = 500;
        responseBody.meta = error;
        if (axios.isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
            responseBody.meta = {
                data: error.config?.data,
                url: error.config?.url,
            };
        }
        return NextResponse.json(responseBody, { status: responseBody.status });
    }

}