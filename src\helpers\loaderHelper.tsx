import React from "react";
import { Backdrop, Box, CircularProgress, Divider, Paper, Typography } from '@mui/material';
import { utilHelper } from "./utilHelper";

const LoaderHelper = ({ tituloLoader, contenido, mostrarLoader }: { tituloLoader: string, contenido: string | JSX.Element, mostrarLoader: boolean }) => {
    return (
        <Backdrop open={mostrarLoader} sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}>
            <Paper sx={{ backgroundColor: 'white', padding: 8 }} elevation={3} square={false}>
                <Box sx={{ display: 'flex', justifyContent: 'center', gap: 4 }}>
                    <CircularProgress color="primary" />
                    <Typography variant="h5" sx={{ fontWeight: 'bold', marginTop: 1 }}>
                        {tituloLoader}
                    </Typography>
                </Box>
                {utilHelper.esValido(contenido) ? (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', marginTop: 2 }}>
                        {contenido}
                    </Box>
                ) : null }
            </Paper>
        </Backdrop>
    );
}

export default LoaderHelper;