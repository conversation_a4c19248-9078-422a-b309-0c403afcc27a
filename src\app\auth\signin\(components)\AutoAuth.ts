"use client"
import ErrorAuth from '@/components/_common/ErrorAuth';
import { AUTH_HOME_ROUTE, RETRY_COUNT, RETRY_INTERVAL } from '@/constants/auth';
import { authHelper } from '@/helpers/authHelper';
import notify from '@/helpers/notifyHelper';
import { utilHelper } from '@/helpers/utilHelper';
import { EAbiType, ErrorAuthUI } from '@/interfaces/utils';
import { authServices } from '@/services/authServices';
import { useAuthStore } from '@/stores/authStore';
import axios from 'axios';
import { signIn, useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

const AutoAuth = ({ urlRedireccion }: { urlRedireccion: string }) => {
    const router = useRouter();
    const params = useSearchParams();
    const { status, data } = useSession();

    const setUserName = useAuthStore((state) => state.setUserName);
    const setUser = useAuthStore((state) => state.setUser);
    const setPermissions = useAuthStore((state) => state.setPermissions);
    const setAuth = useAuthStore((state) => state.setAuth);
    const [errorData, setErrorData] = useState<ErrorAuthUI>({
        status: false,
        message: "",
        subMessage: "",
        description: "",
        abiType: EAbiType.triste,
    });

    const authenticate = async () => {
        const callbackUrl = params.get("callbackUrl") ?? (urlRedireccion != "" ? urlRedireccion : AUTH_HOME_ROUTE);
        let errors: string[] = [params.get("error") ?? ""];
        if (params.get("callbackUrl")) {
            let aux = params.get("callbackUrl") ?? "";
            for (let index = 0; index < RETRY_COUNT; index++) {
                const { error, callbackUrl: callbackUrl_ } = getDataFromCallbackUrl(aux);
                if (error) {
                    if (callbackUrl_) {
                        aux = callbackUrl_ ?? "";
                    }
                    errors.push(error);
                } else {
                    break;
                }
            }
        }
        errors = errors.filter((error) => error !== "");

        let finalCallbackUrl = '/';

        if (callbackUrl == '/') {
            if (utilHelper.esValido(urlRedireccion)) {
                finalCallbackUrl = urlRedireccion;
            }
        } else {
            finalCallbackUrl = callbackUrl;
        }

        if (status === "unauthenticated") {
            setUserName("");
            setUser();
            if (errors.length > 0) {
                let message = errors.includes("OAuthSignin") ? "¡Vaya! Algo no salió bien al iniciar sesión" : "¡Vaya! Algo no salió bien";
                console.error('Error message: ', message);
            }

            if (errors.length <= RETRY_COUNT) {
                setTimeout(() => {
                    signIn("keycloak");
                }, RETRY_INTERVAL);
            } else {
                setErrorData({
                    status: true,
                    message: "¡Algo salió mal!",
                    subMessage: "No se pudo iniciar sesión",
                    description: "Por favor, inténtelo de nuevo más tarde.",
                    abiType: EAbiType.triste,
                })
            }
        } else if (status === "authenticated") {
            setUserName(data?.user.name ?? "");
            const { data: authData } = await authServices.internalAuthApim();
            setAuth(authData);
            authServices.internalGetAuthUser().then((respUser) => {
                const userCode = respUser.data.codigo;
                setUser(respUser.data);
                authServices.internalAuthUserPermissions(userCode).then((respPermissions) => {
                    const permissions = respPermissions.data;
                    setPermissions(permissions);
                    const hasPermission = authHelper.hasPermission(permissions);

                    if (hasPermission) {
                        router.push(finalCallbackUrl);
                    } else {
                        setErrorData({
                            status: true,
                            message: "Ups! Sin acceso...",
                            subMessage: "No tienes permisos para acceder a esta página",
                            description: "Por favor, ponte en contacto con el administrador.",
                            abiType: EAbiType.seguridad,
                        })
                    }
                }).catch((error) => {
                    if (axios.isAxiosError(error) && error?.status == 404) {
                        notify.error("El usuario no tiene permisos asignados");
                    } else {
                        notify.error("¡Vaya! Algo no salió bien al obtener los permisos del usuario");
                    }
                });
            }).catch((error) => {
                if (axios.isAxiosError(error) && error?.status == 404) {
                    notify.error("El usuario no existe");
                } else {
                    notify.error("¡Vaya! Algo no salió bien al obtener los datos del usuario");
                }
            });
        }

    };

    const getDataFromCallbackUrl = (callbackUrl: string) => {
        const url_ = new URL(callbackUrl);
        return {
            error: url_.searchParams.get("error"),
            callbackUrl: url_.searchParams.get("callbackUrl"),
        };
    }

    useEffect(() => {
        authenticate();
    }, [status]);

    if (errorData.status) {
        console.error(errorData);
    }

    return {
        status,
        data
    };
}

export default AutoAuth;