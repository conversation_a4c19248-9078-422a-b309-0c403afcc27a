export interface ClientModel {
    primerNombre: string;
    segundoNombre: string;
    primerApellido: string;
    segundoApellido: string;
    direccion: string;
    barrio: string;
    telefono: string;
    celular: string;
    cedula: string;
    fechaNacimiento: string;
    ocupacion: string;
    fechaAlta: string;
    pais: string;
    localidad: string;
    clasificacion: string;
    codigoSucursal: string;
    codigoOficial: string;
    codigoRegion: string;
    tipoPersona: string;
    tipoDocumento: string;
    sexo: string;
    codigoEstadoCivil: string;
    correo: string;
    codigoCliente: string;
    sucursal: string;
    oficial: string;
    calificacion: string;
    nombreBarrio: string;
    estadoCivil: string;
}

export interface SearchClients extends ClientModel { }

export interface EmployeeModel {
    id: string;
    name: string;
    lastName: string;
    email: string;
    documentNumber: string;
    phone: string;
    position: string;
    salary: string;
    admissionDate: string;
    branch: string;
    status: string;
    isDeleted: boolean;
}

export interface CompanyModel {
    id: string;
    name: string;
}

export interface PreApprovedClientsModel {
    idPreAprobado: string;
    idRegla: string;
    codigoCliente: string;
    numeroDocumento: string;
    tipoDocumento: string;
    cuenta: string;
    promedioAcreditacion: string;
    porcentajeEndeudamiento: string;
    montoPrestamo: string;
    edad: string;
    campanha: string;
    nombreCampanha: string;
    antiguedad: string;
    calificacionConti: string;
    calificacionBCP: string;
    referenciaAtraso: string;
    codeudor: string;
    mora: string;
    sucursal: string;
    ald: string;
    ros: string;
    cantidadPrestamo: string;
    pagoSalario: string;
    montoCuota: string;
    montoCapital: string;
    periodo: string;
    informconf: string;
    estadoBCP: string;
    estadoInformconf: string;
    eliminadoPorRevalidacion: string;
}

export interface TCClientsModel {
    id: string;
    tipoCuenta: string;
    idAfinidad: string;
    departamento: string;
    ciudad: string;
    oficial: string;
    cuenta: string;
    cuentaVip: string;
    cuentaFranqueo: string;
    seguroVida: string;
    cobroCargos: string;
    tipoCosto: string;
    retenerExtracto: string;
    codigoCliente: string;
    linea: string;
    lineaCredito: string;
    cuota: string;
    tipoPago: string;
    valorCFG: string;
    tasaFinanciacion: string;
    factorVip: string;
    motivoRetencion: string;
}

export interface PaginatedResponse<T> {
    items: T[];
    total: number;
    totalPages: number;
}