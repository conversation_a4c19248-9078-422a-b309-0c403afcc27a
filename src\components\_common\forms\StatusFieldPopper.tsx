import { APPROVED_MESSAGE, PENDING_MESSAGE } from '@/constants/parameters';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';

interface StatusFieldPopperProps {
    id: string | undefined;
    open: boolean;
    anchorEl: HTMLElement | null;
    handleClickAway: () => void;
    status: "pending" | "approved";
}
const StatusFieldPopper = ({ id, open, anchorEl, status, handleClickAway }: StatusFieldPopperProps) => {
    const isApproved = status == "approved";
    return (
        <Popper id={id} open={open} anchorEl={anchorEl} placement={"top"} sx={{ zIndex: "100" }}>
            <ClickAwayListener onClickAway={handleClickAway}>
                <Paper
                    sx={{
                        p: 1,
                        bgcolor: isApproved ? "success.main" : "warning.main",
                        marginBottom: "5px",
                        color: "white",
                    }}
                >
                    {isApproved ? APPROVED_MESSAGE : PENDING_MESSAGE}
                </Paper>
            </ClickAwayListener>
        </Popper>
    )
}

export default StatusFieldPopper