import { RequestListView } from "@/interfaces/listView";
import { internalApi } from "@/lib/api";
import { PreApprovedClientsModel } from "@/models/responses/clientServices";
import { ListViewModel } from "@/models/responses/internalService";

const preApprovedView = async ({ pageIndex, pageSize }: RequestListView) => {
    const data = await internalApi.get<ListViewModel<PreApprovedClientsModel>>('pre-approved-view', {
        params: {
            pageIndex,
            pageSize
        }
    });
    return data;
}

const getListView = async () => {
    const data = await internalApi.get('/list-view-pre-approved-clients');
    return data;
}

export const internalService = {
    preApprovedView,
    getListView,
}