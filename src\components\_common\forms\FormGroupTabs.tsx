import { FormGroubTab } from '@/interfaces/forms';
import FormGroupTabContent from './FormGroupTabContent';
import FormGroupTabList from './FormGroupTabList';

interface FormGroupTabsProps {
    tabs: FormGroubTab[];
}
const FormGroupTabs = ({ tabs }: FormGroupTabsProps) => {
    return (
        <>
            <FormGroupTabList tabs={tabs} />
            {
                tabs.map((tab) => (
                    <FormGroupTabContent
                        key={tab.id}
                        groupId={tab.id}
                        formLayout={tab.formLayout}
                    />
                ))
            }
        </>
    )
}

export default FormGroupTabs