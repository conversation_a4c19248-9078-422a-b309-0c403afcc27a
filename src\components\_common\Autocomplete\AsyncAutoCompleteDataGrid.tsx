import { ESearchServices } from '@/constants/utils';
import { useQueryAutoCompleteDataGrid } from '@/hooks/queries/useQueryAutoCompleteDataGrid';
import { OptionListGridItem } from '@/interfaces/_common';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import { SxProps, Theme } from '@mui/material';
import Autocomplete, { AutocompleteChangeDetails, AutocompleteChangeReason, AutocompleteValue } from '@mui/material/Autocomplete';
import Checkbox from '@mui/material/Checkbox';
import Chip from '@mui/material/Chip';
import CircularProgress from '@mui/material/CircularProgress';
import TextField from '@mui/material/TextField';
import React, { useEffect, useState } from 'react';

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

interface AsyncAutoCompleteDataGridProps {
    id: string;
    label: string;
    placeholder?: string;
    searchService: ESearchServices;
    value: OptionListGridItem[];
    onChange: (value: OptionListGridItem[]) => void;
    sx?: SxProps<Theme>;
    disabled?: boolean;
}

const AsyncAutoCompleteDataGrid = (props: AsyncAutoCompleteDataGridProps) => {
    const {
        id,
        label,
        placeholder,
        value,
        onChange,
        searchService,
        sx,
        disabled
    } = props;

    const [open, setOpen] = useState(false);
    const [options, setOptions] = useState<readonly OptionListGridItem[]>([]);

    const handleChange = (
        event: React.SyntheticEvent,
        value: AutocompleteValue<OptionListGridItem, true, false, false>,
        reason: AutocompleteChangeReason,
        details?: AutocompleteChangeDetails<OptionListGridItem>,
    ) => {
        onChange(value);
    }

    const { data, isLoading, isRefetching } = useQueryAutoCompleteDataGrid({ searchService, isOpen: open });
    const loading = isRefetching || isLoading;
    useEffect(() => {
        if (data?.data) {
            setOptions([...data?.data]);
        }
    }, [data]);
    return (
        <Autocomplete<OptionListGridItem, true, false, false>
            sx={sx}
            multiple
            id={id}
            options={options}
            open={open}
            disabled={disabled}
            onOpen={() => {
                setOpen(true);
            }}
            onClose={() => {
                setOpen(false);
            }}
            disableCloseOnSelect
            getOptionLabel={(option) => option.label}
            renderOption={(props, option, { selected }) => (
                <li {...props} key={option.value}>
                    <Checkbox
                        icon={icon}
                        checkedIcon={checkedIcon}
                        style={{ marginRight: 8 }}
                        checked={selected}
                    />
                    {option.label}
                </li>
            )}
            renderTags={(tagValue, getTagProps) => {
                return tagValue.map((option, index) => (
                    <Chip {...getTagProps({ index })} key={option.value} label={option.label} />
                ))
            }}
            loading={loading}
            renderInput={(params) => (
                <TextField
                    {...params}
                    size='small'
                    label={label}
                    placeholder={placeholder}
                    InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                            <React.Fragment>
                                {loading ? <CircularProgress color="inherit" size={20} /> : null}
                                {params.InputProps.endAdornment}
                            </React.Fragment>
                        ),
                    }}
                />
            )}
            onChange={handleChange}
            value={value}
        />
    )
}

export default AsyncAutoCompleteDataGrid