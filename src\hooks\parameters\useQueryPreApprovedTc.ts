import { EFilterControlsTypes } from "@/interfaces/filterControls";
import { preApprovedService } from "@/services/preApprovedService";
import { useFilterDataTableStore } from "@/stores/filterDataTableStore";
import { useQuery } from "@tanstack/react-query";
import dayjs from "dayjs";

const useQueryPreApprovedTc = ({ enableFilter = false }: { enableFilter?: boolean }) => {
    const filters = useFilterDataTableStore(store => store.filters);
    const {
        isPending,
        isLoading,
        isFetched,
        error,
        data,
        refetch: fetchConsult,
        isRefetching,
        isRefetchError,
    } = useQuery({
        queryKey: ['queryPreApprovedTc'],
        queryFn: async () => {
            if (!enableFilter) {
                const resp = await preApprovedService.getInternalPreApprovedTc();
                return {
                    data: resp.data,
                    error: null
                };
            } else {
                const currentDateTime = dayjs();
                const dateMonthFilter = filters.find(f => f.type == EFilterControlsTypes.DateMonth);
                const month = dateMonthFilter?.value ?? currentDateTime.format("MM");
                const dateYearFilter = filters.find(f => f.type == EFilterControlsTypes.DateYear);
                const year = dateYearFilter?.value ?? currentDateTime.format("YYYY");
                const resp = await preApprovedService.getInternalPreApprovedTc(
                    month,
                    year
                );
                return {
                    data: resp.data,
                    error: null
                };
            }
        },
        enabled: true,
        retry: false,
        staleTime: 0,
        gcTime: 0,
    });

    return {
        isPending,
        isLoading,
        isFetched,
        error,
        data,
        fetchConsult,
        isRefetching,
        isRefetchError,
    };
}

export {
    useQueryPreApprovedTc
};
