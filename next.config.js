/** @type {import('next').NextConfig} */

const nextConfig = {
    swcMinify: true,
    poweredByHeader: false,
    minify: {
        html: true,
        css: true,
    },
    compress: true
}

module.exports = nextConfig

module.exports = {
    modularizeImports: {
        '@mui/icons-material': {
            transform: '@mui/icons-material/{{member}}',
        },
    },
    env: {
        NEXT_PUBLIC_KEY_AES: process.env.NEXT_PUBLIC_KEY_AES || '$(NEXT_PUBLIC_KEY_AES)',
        NEXT_PUBLIC_KEYCLOAK_GRANT_TYPE: process.env.NEXT_PUBLIC_KEYCLOAK_GRANT_TYPE || '$(NEXT_PUBLIC_KEYCLOAK_GRANT_TYPE)',
        NEXT_PUBLIC_KEYCLOAK_ID: process.env.NEXT_PUBLIC_KEYCLOAK_ID || '$(NEXT_PUBLIC_KEYCLOAK_ID)',
        NEXT_PUBLIC_KEYCLOAK_ISSUER: process.env.NEXT_PUBLIC_KEYCLOAK_ISSUER || '$(NEXT_PUBLIC_KEYCLOAK_ISSUER)',
        NEXT_PUBLIC_KEYCLOAK_SECRET: process.env.NEXT_PUBLIC_KEYCLOAK_SECRET || '$(NEXT_PUBLIC_KEYCLOAK_SECRET)',
        NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET || '$(NEXTAUTH_SECRET)',
        NEXTAUTH_URL: process.env.NEXTAUTH_URL || '$(NEXTAUTH_URL)',
        NEXT_PUBLIC_SUBSCRIPTION_KEY: process.env.NEXT_PUBLIC_SUBSCRIPTION_KEY || '$(NEXT_PUBLIC_SUBSCRIPTION_KEY)',
        NEXT_PUBLIC_VALID_HOST: process.env.NEXT_PUBLIC_VALID_HOST || '$(NEXT_PUBLIC_VALID_HOST)',
        NEXT_PUBLIC_BASE_API_URL: process.env.NEXT_PUBLIC_BASE_API_URL || '$(NEXT_PUBLIC_BASE_API_URL)',
        NEXT_PUBLIC_URL_REDIRECT: process.env.NEXT_PUBLIC_URL_REDIRECT || '$(NEXT_PUBLIC_URL_REDIRECT)',
        NEXT_PUBLIC_API_TIME_OUT: process.env.NEXT_PUBLIC_API_TIME_OUT || '$(NEXT_PUBLIC_API_TIME_OUT)'
    },
    output: 'standalone',
    headers: async () => {
        return [
            {
                source: '/:path*{/}?',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'no-store',
                    },
                ],
            },
        ]
    },

}