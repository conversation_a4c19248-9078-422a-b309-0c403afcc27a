"use client"
import { TValues } from "@/interfaces/_common";
import { DefaultValues, Resolver, UseFormReturn, useForm } from "react-hook-form";

interface UseCustomFormProps<TData extends TValues> {
    defaultValues: DefaultValues<TData>;
    resolver: Resolver<TData>;
}

const useCustomForm = <TData extends TValues>(props: UseCustomFormProps<TData>) => {
    const { defaultValues, resolver } = props;
    const methods: UseFormReturn<TData> = useForm<TData>({
        reValidateMode: 'onChange',
        resolver,
        defaultValues: defaultValues,
    });

    return methods;
};

export default useCustomForm;