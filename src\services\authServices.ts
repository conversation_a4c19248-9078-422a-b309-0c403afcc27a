import { externalEndpoints, internalEndpoints } from "@/config/endpoints";
import { AuthKeycloackHeader } from "@/interfaces/auth";
import { baseApi, internalApi } from "@/lib/api";
import { AuthResponseModel, AuthUserModel, UserPermissionsResponseModel } from "@/models/responses/authServices";

const authApim = async () => {
    const keycloackHeaders: AuthKeycloackHeader = {
        "Client-Id": process.env.NEXT_PUBLIC_KEYCLOAK_ID as string,
        "Client-Secret": process.env.NEXT_PUBLIC_KEYCLOAK_SECRET as string,
        "Grant-Type": process.env.NEXT_PUBLIC_KEYCLOAK_GRANT_TYPE as string,
        "Subscription-Key": process.env.NEXT_PUBLIC_SUBSCRIPTION_KEY as string,
        Scope: "profile",
    }
    const resp = await baseApi.post<AuthResponseModel>(externalEndpoints.auth.apim, {}, {
        headers: {
            ...keycloackHeaders
        }
    });
    return resp;
};

const getAuthUser = async (userId: string) => {
    const resp = await baseApi.get<AuthUserModel>(externalEndpoints.auth.obtenerDatosUsuario(userId));
    return resp;
};

const getAuthUserPermissions = async (userId: string) => {
    const resp = await baseApi.get<UserPermissionsResponseModel[]>(externalEndpoints.auth.obtenerPermisosUsuario(userId));
    return resp;
};

const internalAuthApim = async () => {
    const resp = await internalApi.post<AuthResponseModel>(internalEndpoints.auth.apim, {});
    return resp;
}

const internalGetAuthUser = async () => {
    const resp = await internalApi.get<AuthUserModel>(internalEndpoints.auth.obtenerDatosUsuario);
    return resp;
}

const internalAuthUserPermissions = async (userCode: string) => {
    const resp = await internalApi.get<UserPermissionsResponseModel[]>(internalEndpoints.auth.obtenerPermisosUsuario, {
        params: {
            userCode
        }
    });
    return resp;
}

const internalLogout = async () => {
    const data = await internalApi.post('auth/logout');
    return data;
}

const internalHasPermission = async (userCode: string) => {
    const data = await internalApi.get('auth/hasPermission', { params: { userCode } });
    return data;
}

export const authServices = {
    authApim,
    getAuthUser,
    getAuthUserPermissions,
    internalAuthApim,
    internalGetAuthUser,
    internalAuthUserPermissions,
    internalLogout,
    internalHasPermission
}