import AuthApiWrapper from '@/auth/AuthWrapper'
import ThemeRegistry from '@/components/ThemeRegistry/ThemeRegistry'
import MainAppBar from '@/components/partials/MainAppBar'
import MainDrawer from '@/components/partials/MainDrawer'
import { Container, Toolbar } from '@mui/material'
import Box from '@mui/material/Box'
import type { Metadata } from 'next'
import localFont from "next/font/local";
import './globals.css'
const customFont = localFont({ src: "../themes/fonts/ContiSansRegular.woff2" });
import Providers from './providers'

export const metadata: Metadata = {
    title: 'Banco Continental',
    description: 'Preaprobados',
}

export default function RootLayout(props: { children: React.ReactNode }) {
    const { children } = props
    return (
        <html lang="es">
            <body className={customFont.className}>
                <ThemeRegistry options={{ key: 'mui' }}>
                    <Providers>
                        <AuthApiWrapper>
                            <Box
                                sx={{
                                    display: 'flex',
                                }}
                            >
                                <MainAppBar />
                                <MainDrawer />
                                <Container
                                    maxWidth="xl"
                                    component="main"
                                    sx={{
                                        flexGrow: 1,
                                        bgcolor: 'background.default',
                                    }}
                                >
                                    <Toolbar />
                                    <Box sx={{
                                        height: "calc(100vh - 64px)",
                                        paddingTop: 2,
                                    }}>
                                        {children}
                                    </Box>
                                </Container>
                            </Box>
                        </AuthApiWrapper>

                    </Providers>
                </ThemeRegistry>
            </body>
        </html>
    )
}