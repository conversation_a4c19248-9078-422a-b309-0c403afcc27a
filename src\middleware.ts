import { PUBLIC_ROUTES } from "@/constants/auth";
import { withAuth } from "next-auth/middleware"

export default withAuth({
    callbacks: {
        authorized({ req, token }) {
            const isPublicRoute = PUBLIC_ROUTES.includes(req.nextUrl.pathname);
            if (token || isPublicRoute) {
                return true;
            }
            return false;
        },
    },
})

export const config = {
    matcher: [
        '/((?!api|_next/static|_next/image|favicon.ico|images).*)',
    ],
}