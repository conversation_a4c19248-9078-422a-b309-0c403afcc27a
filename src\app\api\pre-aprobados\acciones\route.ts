import { preAprobadosApi } from "@/lib/api";
import { RolesParametros } from "@/models/responses/usuariosServices";
import { usuarioServices } from "@/services/usuarioServices";
import axios from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    data: RolesParametros[] | undefined,
    error?: string
    status: number
    meta?: any
}
export async function GET(req: NextRequest) {
    let responseBody: Response = {
        data: undefined,
        error: undefined,
        status: 200
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 401;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    preAprobadosApi.defaults.nextRequest = req;

    try {
        const resp = await usuarioServices.getAcciones();
        responseBody.data = resp.data;
        return NextResponse.json(responseBody.data, { status: 200 });
    } catch (error: any) {
        responseBody.error = "Error al obtener las acciones parametricas de Pre Aprobados.";
        responseBody.status = 500;
        responseBody.meta = error;
        if (axios.isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
            responseBody.meta = {
                data: error.config?.data,
                url: error.config?.url,
            };
        }
        console.error(error);
        return NextResponse.json(responseBody, { status: responseBody.status });
    }
}