'use client'
import { utilHelper } from '../../../helpers/utilHelper';
import notify from "@/helpers/notifyHelper";
import { AuthUserModel } from "@/models/responses/authServices";
import { InsertAccionesUsuario, RolesParametros, RolesUsuarioResponse, UpdateAccionesUsuario } from "@/models/responses/usuariosServices";
import { usuarioServices } from "@/services/usuarioServices";
import { SelectChangeEvent } from "@mui/material";
import axios from "axios";
import { closeSnackbar } from 'notistack';
import { useEffect, useMemo, useState } from "react";

const UseConfigurarAcciones = () => {
    const [datosUsuario, setDatosUsuario] = useState<AuthUserModel>();
    const [usuarioDominio, setUsuarioDominio] = useState("");
    const [mostrarDatosUsuario, setMostrarDatosUsuario] = useState(false);
    const [esperandoRespuesta, setEsperandoRespuesta] = useState(false);
    const [openModal, setOpenModal] = useState(false);
    const [rolesUsuario, setRolesUsuario] = useState<RolesUsuarioResponse[]>([]);
    const [rolesParametros, setRolesParametros] = useState<RolesParametros[]>([]);
    const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
    const [originalRoles, setOriginalRoles] = useState<string[]>([]);
    const [cargandoRoles, setCargandoRoles] = useState<boolean>(false);

    const handleBuscarClick = (): void => {
        obtenerUsuario();
    }

    const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Enter') {
            event.preventDefault();
            obtenerUsuario();
        }
    }

    const codigoUsuario: string = useMemo(() => {
        return datosUsuario?.codigo ?? '';
    }, [datosUsuario]);

    useEffect(() => {
        if (codigoUsuario) {
            const fetchData = async () => {
                await obtenerAccionesUsuario();
            };

            fetchData();
        }
    }, [codigoUsuario]);

    const obtenerAcciones = async () => {
        try {
            const response = await usuarioServices.getAccionesInterno();
            const roles = response.data;
            setRolesParametros(roles);
        } catch (error) {
            console.error("Error al obtener roles:", error);
        }
    };

    const obtenerUsuario = async () => {
        setEsperandoRespuesta(true);
        try {
            let respUser = await usuarioServices.getUsuarioPorDominioInterno(usuarioDominio);
            if (utilHelper.esValido(respUser?.data)) {
                setEsperandoRespuesta(false);
                setDatosUsuario(respUser.data);
                setMostrarDatosUsuario(true);
            }
        } catch (error) {
            setDatosUsuario(undefined);
            setEsperandoRespuesta(false);
            setMostrarDatosUsuario(false);
            if (axios.isAxiosError(error) && error?.response?.status == 404) {
                notify.error("El usuario no existe");
            } else {
                notify.error("¡Vaya! Algo no salió bien al obtener los datos del usuario");
            }
        } finally {
            setEsperandoRespuesta(false);
        }
    }

    const obtenerAccionesUsuario = async () => {
        try {
            setCargandoRoles(true);

            if (!utilHelper.esValido(codigoUsuario)) return false;

            const respRoles = await usuarioServices.getAccionesUsuarioInterno(codigoUsuario);

            if (utilHelper.esValido(respRoles.data)) {

                setCargandoRoles(false);
                const rolesAsignados: RolesParametros[] = respRoles.data.map((rol: RolesUsuarioResponse) => ({
                    idRolUsuario: rol.idRolUsuario,
                    idRol: rol.idRol,
                    descripcion: rol.descripcion,
                    estado: rol.estado
                }));

                setRolesUsuario(respRoles.data);

                setSelectedRoles(rolesAsignados
                    .map(rol => rolesParametros.find(rolParam => rolParam.idRol === rol.idRol && rol.estado === 'A'))
                    .filter(Boolean)
                    .map(rol => rol?.descripcion ?? ''));
            
            } else {
                setRolesUsuario([]);
                setSelectedRoles([]);
                setCargandoRoles(false);
            }
        } catch (error) {
            setCargandoRoles(false);
            console.error("Error al obtener roles del usuario:", error);
        } finally {
            setCargandoRoles(false);
        }
    }

    const handleOpenModal = async () => {
        if (Array.isArray(rolesUsuario) && rolesUsuario.length > 0) {
            const updatedRolesParametros = rolesParametros.map(rolParam => {
                const selected = rolesUsuario.some(rolUser => rolUser.idRol === rolParam.idRol && rolUser.estado == 'A');
                return { ...rolParam, estado: selected ? 'A' : 'I' };
            });
            setSelectedRoles(updatedRolesParametros.filter(rol => rol.estado === 'A').map(rol => rol.descripcion));
        }

        setOpenModal(true);
    }

    const handleCloseModal = async () => {
        setOpenModal(false);
        setSelectedRoles(originalRoles);
        await obtenerAccionesUsuario();
    }

    const handleChangeSelectedRoles = (event: SelectChangeEvent<string | string[]>) => {
        const { value } = event.target;
        const newValue = Array.isArray(value) ? value : [value];

        setSelectedRoles(newValue);

        const updatedRoles = rolesParametros.map(rol => ({
            ...rol,
            estado: newValue.includes(rol.descripcion) ? 'A' : 'I'
        }));

        setRolesParametros(updatedRoles);
    };

    const handleGuardarCambios = async () => {
        let requiereInsercion = false;
        let insertRequest: InsertAccionesUsuario = {
            codigoUsuario: '',
            accionesUsuario: []
        };
        if (!utilHelper.esValido(datosUsuario?.codigo)) return false;


        rolesParametros.forEach(rol => {
            let idRolUsuario = rolesUsuario.find(rolUser => rolUser.idRol === rol.idRol)?.idRolUsuario ?? 0;
            if (idRolUsuario === 0) {
                requiereInsercion = true;
                insertRequest.codigoUsuario = datosUsuario?.codigo ?? '';
                insertRequest.accionesUsuario.push({
                    idRol: rol.idRol,
                    estado: selectedRoles.includes(rol.descripcion) ? 'A' : 'I'
                });
            }
        });

        const updateRequest: UpdateAccionesUsuario = {
            codigoUsuario: datosUsuario?.codigo ?? '',
            accionesUsuario: rolesParametros
                .filter(rol => {
                    const idRolUsuario = rolesUsuario.find(rolUser => rolUser.idRol === rol.idRol)?.idRolUsuario ?? 0;
                    return idRolUsuario !== 0;
                })
                .map(rol => {
                    const idRolUsuario = rolesUsuario.find(rolUser => rolUser.idRol === rol.idRol)?.idRolUsuario ?? 0;
                    return {
                        idRolUsuario,
                        estado: selectedRoles.includes(rol.descripcion) ? 'A' : 'I'
                    };
                })
        };

        if (requiereInsercion) {
            await insertAccionesUser(insertRequest);
        }

        if (updateRequest.accionesUsuario.length > 0) {
            await updateAccionesUser(updateRequest);
        }
    };

    const updateAccionesUser = async (updateRequest: UpdateAccionesUsuario) => {
        notify.loader('Guardando cambios...');
        try {
            const response = await usuarioServices.updateAccionesUsuarioInterno(updateRequest);
            const data = response?.data;
            if (data === "OK") {
                closeSnackbar();
                notify.success("Cambios actualizados correctamente.");
                setOriginalRoles(selectedRoles);
                handleCloseModal();
            }
        } catch (error) {
            closeSnackbar();
            console.error("Error al guardar cambios:", error);
            notify.error("¡Vaya! Algo no salió bien al actualizar los cambios.");
        }
    }

    const insertAccionesUser = async (insertRequest: InsertAccionesUsuario) => {
        notify.loader('Guardando cambios...');
        try {
            const response = await usuarioServices.insertAccionesUsuarioInterno(insertRequest);
            const data = response?.data;
            if (data === "OK") {
                closeSnackbar();
                notify.success("Se agregaron nuevas acciones para el usuario correctamente.");
                setOriginalRoles(selectedRoles);
                handleCloseModal();
            }
        } catch (error) {
            closeSnackbar();
            console.error("Error al guardar cambios:", error);
            notify.error("¡Vaya! Algo no salió bien al guardar agregar nuevas acciones al usuario.");
        }
    }

    return {
        datosUsuario,
        mostrarDatosUsuario,
        esperandoRespuesta,
        openModal,
        rolesUsuario,
        rolesParametros,
        selectedRoles,
        cargandoRoles,
        handleBuscarClick,
        handleKeyPress,
        obtenerAcciones,
        handleOpenModal,
        handleCloseModal,
        handleChangeSelectedRoles,
        handleGuardarCambios,
        setOriginalRoles,
        setUsuarioDominio,
        obtenerAccionesUsuario
    }
}

export default UseConfigurarAcciones;