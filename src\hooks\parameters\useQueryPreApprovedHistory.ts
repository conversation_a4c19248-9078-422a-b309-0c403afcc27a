import { useQuery } from "@tanstack/react-query";

interface Props {
    page: number;
    pageSize: number;
}
const useQueryPreApprovedHistory = ({ page, pageSize }: Props) => {
    const {
        isPending,
        isLoading,
        isFetched,
        error,
        data,
        refetch: fetchConsult,
        isRefetching,
        isRefetchError,
    } = useQuery({
        queryKey: ['queryPreApprovedHistory', page, pageSize],
        queryFn: async () => {
            return {
                data: null,
                error: null
            };
        },
        enabled: false,
        retry: false
    });

    return {
        isPending,
        isLoading,
        isFetched,
        error,
        data,
        fetchConsult,
        isRefetching,
        isRefetchError,
    };
}

export {
    useQueryPreApprovedHistory
};