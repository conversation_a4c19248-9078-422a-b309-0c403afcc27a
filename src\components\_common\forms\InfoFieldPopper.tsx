import ClickAwayListener from '@mui/material/ClickAwayListener';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';

interface InfoFieldPopperProps {
    id: string | undefined;
    open: boolean;
    anchorEl: HTMLElement | null;
    info: string;
    handleClickAway: () => void;
}
const InfoFieldPopper = ({ id, open, anchorEl, info, handleClickAway }: InfoFieldPopperProps) => {
    return (
        <Popper id={id} open={open} anchorEl={anchorEl} placement={"top"} sx={{ zIndex: "100" }}>
            <ClickAwayListener onClickAway={handleClickAway}>
                <Paper
                    sx={{
                        p: 1,
                        bgcolor: 'gray',
                        marginBottom: "5px",
                        color: "white",
                    }}
                >
                    {info}
                </Paper>
            </ClickAwayListener>
        </Popper>
    )
}

export default InfoFieldPopper