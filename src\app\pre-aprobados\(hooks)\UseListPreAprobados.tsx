import { preApprovedService } from "@/services/preApprovedService";
import { utilHelper } from "@/helpers/utilHelper";
import { useState } from "react";
import * as XLSX from "xlsx";
import {
    Box,
    Typography,
    Divider
} from '@mui/material';
import { CONTENIDO_LOADER } from "../(enums)/LoaderEnum";
import { FORMATO_EXPORTACION_EXCEL, ORIGEN_CONSULTA } from "../(enums)/UtilEnum";
import notify from "@/helpers/notifyHelper";
import { closeSnackbar } from "notistack";
import { COLUMNA_FORMATEO_EXCEL } from "../(enums)/FormPreAprobadosPgs";

function toPascalCase(text: string) {
  return text
    .toLowerCase()
    .replace(/(?:^|\s|_)(\w)/g, (_, c) => c.toUpperCase());
}

const UseListPreAprobados = (
    {
        onFormatColumnValue,
        filters,
        columns,
        origenExportacion
    }:
    {
        onFormatColumnValue: (columna: string, valor: any) => any,
        filters: any,
        columns: any,
        origenExportacion: string
    }) => {
    const [mostrarLoaderExcel, setMostrarLoaderExcel] = useState<boolean>(false);
    const reformateoResultado = (columnId: string, value: any) => {
        if (columnId == COLUMNA_FORMATEO_EXCEL.CUENTA || columnId == COLUMNA_FORMATEO_EXCEL.ESTADO_INFORMCONF || columnId == COLUMNA_FORMATEO_EXCEL.ESTADO_BCP
            || columnId == COLUMNA_FORMATEO_EXCEL.SUCURSAL || columnId == COLUMNA_FORMATEO_EXCEL.REVALIDADO || columnId == COLUMNA_FORMATEO_EXCEL.ELIMINADO_REVALIDACION) {
            return utilHelper.esValido(value) && value != null ? value?.props?.label : '';
        }

        return value || '';
    };

    const getNombreArchivo = () => {
        if (origenExportacion === ORIGEN_CONSULTA.HISTORICO) {
            return FORMATO_EXPORTACION_EXCEL.NOMBRE_ARCHIVO_HISTORICO;
        } else if (origenExportacion === ORIGEN_CONSULTA.LISTADO) {
            return FORMATO_EXPORTACION_EXCEL.NOMBRE_ARCHIVO_LISTA_PREAPROBADOS;
        }
        return FORMATO_EXPORTACION_EXCEL.NOMBRE_DEFAULT;
    };

    const informacionLoader = () => {
        const archivoNombre = getNombreArchivo();
        return (
            <>
                <Box sx={{ display: 'flex', width: '100%' }}>
                    <Divider variant="fullWidth" sx={{ margin: 2, width: '95%', color: '#1D428A' }}>
                        DETALLES DEL PROCESO
                    </Divider>
                </Box>
                <Box sx={{ display: 'flex', width: '100%' }}>
                    <Typography align='center' variant='h6' fontWeight='bold' sx={{ marginLeft: 2, marginTop: 2, width: '100%' }}>
                        {CONTENIDO_LOADER.ARCHIVO_EXCEL} {archivoNombre}.{FORMATO_EXPORTACION_EXCEL.FORMATO_ARCHIVO}
                    </Typography>
                </Box>
            </>
        );
    }

    const confirmarExportacion = () => {
        let textoMensaje = "¿Está seguro que desea exportar estos datos a Excel?";
        notify.confirmation({
            message: textoMensaje,
            onConfirm: (snackbarId) => {
                closeSnackbar(snackbarId);
                const confirmHandler = async () => {
                    await exportToExcel();
                };
                confirmHandler();
            },
        });
    }

    const exportToExcel = async () => {
        try {
            setMostrarLoaderExcel(true);
            let respuestaServicio: any = {};
            const parametroHistoricos: any = await preApprovedService.getHistoricParameters();
            const parametrosActuales: any = await preApprovedService.getParameters('1', 'approved');
            let resultado = parametroHistoricos.data.count > 0 ? parametroHistoricos : parametrosActuales;
            
            if(origenExportacion == ORIGEN_CONSULTA.HISTORICO){
                const params = {
                    ...filters,
                    numeroPaginaActual: 1,
                    cantidadRegistrosPagina: 999999,
                };
                respuestaServicio = await preApprovedService.getInternalPreApprovedClientsHistory(params);
            } else if (origenExportacion == ORIGEN_CONSULTA.LISTADO){
                const params = {
                    ...filters,
                    numeroPaginaActual: 1,
                    cantidadRegistrosPagina: 999999,
                    periodo: utilHelper.calculatePeriodo()
                };
                respuestaServicio = await preApprovedService.getInternalPreApprovedClients(params);
            }
            const newColumns = columns;

            resultado.data.forEach((data: any) => {
                newColumns.push({ id: toPascalCase(data.nombreParametro), label: data.nombreParametro, minWidth: 100, align: 'center' });
            });
            
            if (utilHelper.esValido(respuestaServicio?.data?.datos)) {
                const data = respuestaServicio.data.datos.map((row: any) => {

                    const parametros = resultado.data.filter(
                        (param: any) => (param.idHistoricoRegla ?? param.idRegla) === row.idRegla
                    );

                    const formattedRow: any = {};                    
                    newColumns.forEach((column: any) => {
                        formattedRow[column.label] = reformateoResultado(column.id, onFormatColumnValue(column.id, row[column.id]));
                    });

                    parametros.forEach((param: any) => {
                        let columnId = toPascalCase(param.nombreParametro);
                        formattedRow[param.nombreParametro] = reformateoResultado(columnId, onFormatColumnValue(columnId, param.valorDesde));
                    });
             
                    return formattedRow;
                });
                const workbook = XLSX.utils.book_new();
                const worksheet = XLSX.utils?.json_to_sheet(data);
                XLSX.utils.book_append_sheet(workbook, worksheet, );
                XLSX.writeFile(workbook, `${getNombreArchivo()}.${FORMATO_EXPORTACION_EXCEL.FORMATO_ARCHIVO}`);
            } else {
                notify.error("No se encontraron registros para exportar.");
            }
        } catch (error) {
            notify.error(`Ocurrio un error al exportar los datos a Excel. \n ${getNombreArchivo()}.${FORMATO_EXPORTACION_EXCEL.FORMATO_ARCHIVO}`)
            console.error('Error al exportar datos: ', error);
        } finally {
            setMostrarLoaderExcel(false);
        }
    };

    return {
        confirmarExportacion,
        informacionLoader,
        mostrarLoaderExcel
    }
}

export default UseListPreAprobados;