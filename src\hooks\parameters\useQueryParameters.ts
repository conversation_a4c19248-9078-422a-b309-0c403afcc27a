import { utilHelper } from "@/helpers/utilHelper";
import { ClientModel } from "@/models/responses/clientServices";
import { ParametersModel } from "@/models/responses/parameterService";
import { preApprovedService } from "@/services/preApprovedService";
import { useUtilStore } from "@/stores/utilStore";
import { useQuery } from "@tanstack/react-query";
import { isAxiosError } from "axios";

const useQueryParameters = (groupId: string) => {
    const setClients = useUtilStore(state => state.setClients);
    const {
        isPending,
        isLoading,
        isFetched,
        error,
        data: fetchedData,
        refetch: fetchConsult,
        isRefetching,
        isRefetchError,
    } = useQuery({
        queryKey: ['queryParameters', groupId],
        queryFn: async () => {
            let data: ParametersModel[] = [];
            let error = null;
            try {
                const dataApproved = await preApprovedService.getInternalParameters(groupId, "approved");
                const dataPending = await preApprovedService.getInternalParameters(groupId, "pending");
                data = dataApproved.data.concat(dataPending.data);

                const empresas = data.find(d => d.idParametros == "29");

                if (empresas) {
                    const codigoEmpresas: string[] = empresas.valorDesde
                        ?.split(",")
                        ?.filter((item: string) => item !== "") ?? [];

                    const clients: ClientModel[] = [];
                    for await (const client of utilHelper.obtenerEmpresas(codigoEmpresas)) {
                        clients.push(client);
                    }
                    setClients(clients);
                }
            } catch (error_: any) {
                if (isAxiosError(error_)) {
                    error = error_.message;
                } else {
                    error = "Error al obtener los Parámetros";
                }
            }

            return {
                data,
                error
            };
        },
        enabled: true,
        refetchOnMount: true,
        staleTime: 0,
    });

    return {
        isPending,
        isLoading,
        isFetched,
        error,
        data: fetchedData,
        fetchConsult,
        isRefetching,
        isRefetchError,
    };
}


export {
    useQueryParameters
};