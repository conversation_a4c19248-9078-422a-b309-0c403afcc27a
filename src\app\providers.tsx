'use client'
import { R<PERSON><PERSON>CH_INTERVAL_SESSION } from "@/constants/auth"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { SessionProvider } from "next-auth/react"
import { SnackbarProvider } from "notistack"
import { useState } from "react"

interface ProvidersProps {
    children: React.ReactNode
}
export default function Providers({ children }: ProvidersProps) {
    const [queryClient] = useState(() => new QueryClient({
        defaultOptions: {
            queries: {
                refetchOnWindowFocus: false,
                retry: false,
                staleTime: 0
            },
        },
    }))
    return (
        <>
            <SessionProvider refetchInterval={REFETCH_INTERVAL_SESSION} refetchOnWindowFocus={false}>
                <SnackbarProvider>
                    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
                </SnackbarProvider>
            </SessionProvider>
        </>
    )
}