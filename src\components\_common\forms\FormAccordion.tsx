import { TValues } from '@/interfaces/_common';
import { FormLayout } from '@/interfaces/forms';
import { memo } from 'react';
import FormAccordionItem from './FormAccordionItem';

interface FormAccordionProps<TData extends TValues> {
    formLayout: FormLayout<TData>;
}
const FormAccordion = memo(function FormAccordion<TData extends TValues>({ formLayout }: FormAccordionProps<TData>) {
    const groups = formLayout.groups;
    return (
        <>
            {groups.map((item, index) => (
                <FormAccordionItem key={index} itemGroup={item} index={index} />
            ))}
        </>
    )
}

) as <TData extends TValues>(props: FormAccordionProps<TData>) => JSX.Element;

export default FormAccordion