import notify from '@/helpers/notifyHelper';
import { util<PERSON>elper } from '@/helpers/utilHelper';
import { UserPermissionsResponseModel } from '@/models/responses/authServices';
import { authServices } from '@/services/authServices';
import { usuarioServices } from '@/services/usuarioServices';

export const verificarPermisos = async (emailUsuario: string): Promise<boolean> => {
    try {
        const urlActual = window.location.href;
        const urlObj = new URL(urlActual);
        let rutaRelativa = urlObj.pathname;
        rutaRelativa = rutaRelativa.substring(1);
        if (!utilHelper.esValido(emailUsuario)) return false;
        let usuarioDominio = emailUsuario.split('@')[0];
        if (!utilHelper.esValido(usuarioDominio)) return false;
        let datosUsuario = await usuarioServices.getUsuarioPorDominioInterno(usuarioDominio);
        if (!utilHelper.esValido(datosUsuario)) return false;
        const permissionsResponse = await authServices.internalAuthUserPermissions(datosUsuario?.data?.codigo);
        if (permissionsResponse.data.length === 0) return false;

        return permissionsResponse.data.some((objeto: UserPermissionsResponseModel) => objeto.url === process.env.NEXT_PUBLIC_VALID_HOST + rutaRelativa);
    } catch (error) {
        notify.error("Ocurrió un error al verificar permisos.");
        console.error("Error al verificar permisos:", error);
        return false;
    }
};