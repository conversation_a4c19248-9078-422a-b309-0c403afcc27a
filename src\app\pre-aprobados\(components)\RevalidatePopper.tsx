import ClickAwayListener from '@mui/material/ClickAwayListener';
import IconButton from '@mui/material/IconButton';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';

interface RevalidatePopperProps {
    id: string | undefined;
    open: boolean;
    anchorEl: HTMLElement | null;
    handleClickAway: () => void;
}
const RevalidatePopper = ({ id, open, anchorEl, handleClickAway }: RevalidatePopperProps) => {
    return (
        <Popper id={id} open={open} anchorEl={anchorEl} placement={"top"} sx={{ zIndex: "100" }}>
            <ClickAwayListener onClickAway={handleClickAway}>
                <Paper
                    sx={{
                        p: 1,
                        marginBottom: "5px",
                        color: "white",
                    }}
                >
                    <IconButton
                        disabled={false}
                        sx={{
                            backgroundColor: "primary.main",
                            color: "white",
                            borderRadius: 1,
                            "&:hover": {
                                backgroundColor: "primary.dark",
                            },
                            "&:disabled": {
                                backgroundColor: "primary.light",
                            },
                        }}
                        onClick={() => {}}
                        size="small"
                    >
                        BCP
                    </IconButton>
                    <IconButton
                        disabled={false}
                        sx={{
                            backgroundColor: "primary.main",
                            color: "white",
                            borderRadius: 1,
                            "&:hover": {
                                backgroundColor: "primary.dark",
                            },
                            "&:disabled": {
                                backgroundColor: "primary.light",
                            },
                        }}
                        onClick={() => {}}
                        size="small"
                    >
                        BCP
                    </IconButton>
                    <IconButton
                        disabled={false}
                        sx={{
                            backgroundColor: "primary.main",
                            color: "white",
                            borderRadius: 1,
                            "&:hover": {
                                backgroundColor: "primary.dark",
                            },
                            "&:disabled": {
                                backgroundColor: "primary.light",
                            },
                        }}
                        onClick={() => {}}
                        size="small"
                    >
                        BCP
                    </IconButton>
                </Paper>
            </ClickAwayListener>
        </Popper>
    )
}

export default RevalidatePopper