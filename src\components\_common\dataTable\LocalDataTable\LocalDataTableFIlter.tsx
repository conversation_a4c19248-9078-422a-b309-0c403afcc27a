import { TValues } from "@/interfaces/_common";
import InputBase from "@mui/material/InputBase";
import { Column, Table as ReactTable } from "@tanstack/react-table";

interface LocalTableProps<TData extends TValues> {
    column: Column<TData, unknown>;
    table: ReactTable<TData>
}
const LocalDataTableFilter = <TData extends TValues>({ column, table, }: LocalTableProps<TData>) => {
    const firstValue = table
        .getPreFilteredRowModel()
        .flatRows[0]?.getValue(column.id)

    const columnFilterValue = column.getFilterValue()

    return typeof firstValue === 'number' ? (
        <div className="flex space-x-2">
            <InputBase
                type="number"
                value={(columnFilterValue as [number, number])?.[0] ?? ''}
                onChange={e =>
                    column.setFilterValue((old: [number, number]) => [
                        e.target.value,
                        old?.[1],
                    ])
                }
                placeholder={`Min`}
                className="w-24 border shadow rounded"
            />
            <InputBase
                type="number"
                value={(columnFilterValue as [number, number])?.[1] ?? ''}
                onChange={e =>
                    column.setFilterValue((old: [number, number]) => [
                        old?.[0],
                        e.target.value,
                    ])
                }
                placeholder={`Max`}
                className="w-24 border shadow rounded"
                inputProps={{ 'aria-label': 'search' }}
            />
        </div>
    ) : (
        <InputBase
            value={(columnFilterValue ?? '') as string}
            onChange={e => column.setFilterValue(e.target.value)}
            placeholder={`Buscar...`}
            className="w-36 border shadow rounded"
            inputProps={{ 'aria-label': 'search' }}
        />
    )
}

export default LocalDataTableFilter;