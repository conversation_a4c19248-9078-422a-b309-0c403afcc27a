import { ESearchServices } from '@/constants/utils';
import { OptionListGridItem, TValues } from '@/interfaces/_common';
import { MRT_Header, MRT_TableInstance } from 'material-react-table';
import { useState, useTransition } from 'react';
import AsyncAutoCompleteDataGrid from '../Autocomplete/AsyncAutoCompleteDataGrid';

interface AsyncMultiSelectProps<TData extends TValues = any> {
    header: MRT_Header<TData>;
    table: MRT_TableInstance<TData>;
    searchService: ESearchServices;
}
const AsyncMultiSelect = <TData extends TValues>({ header, table, searchService }: AsyncMultiSelectProps<TData>) => {
    const { column } = header;

    const [isPending, startTransition] = useTransition();
    const [filterValues, setFilterValues] = useState<OptionListGridItem[]>([]);

    return (
        <AsyncAutoCompleteDataGrid
            id={column.id}
            label={column.columnDef.header}
            searchService={searchService}
            value={filterValues}
            onChange={(value: OptionListGridItem[]) => {
                setFilterValues(value);

                startTransition(() => {
                    column.setFilterValue(value.map(x => x.value));
                });
            }}
        />
    )
}

export default AsyncMultiSelect