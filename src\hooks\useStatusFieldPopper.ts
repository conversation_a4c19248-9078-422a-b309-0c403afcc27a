import { useState } from "react";

const useStatusFieldPopper = (id: string) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

    const handleClickInfo = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(anchorEl ? null : event.currentTarget);
    }
    const open = Boolean(anchorEl);
    const id_ = open ? `p-${id}` : undefined;
    const handleClickAway = () => {
        setAnchorEl(null);
    };

    return {
        handleClickInfo,
        anchorEl,
        open,
        id: id_,
        handleClickAway
    };
};

export {
    useStatusFieldPopper
};