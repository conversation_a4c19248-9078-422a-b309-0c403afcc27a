import { CURRENCY } from "@/constants/_common";
import { COLUMNS_DEFS, ESearchServices, OPTIONS_ITEMS } from "@/constants/utils";
import { FormDataType, FormGroup, FormLayout } from "@/interfaces/forms";
import { PreApprovedParameters } from "@/interfaces/parameters";

const grupoCamposGenerales: FormGroup<PreApprovedParameters> = {
    id: "1",
    name: "basic_information",
    label: "GENERALES",
    fields: [
        {
            id: "calificacionSisFinanciero",
            label: "Calificación del sistema Financiero (Banco Continental)",
            name: "calificacionSisFinanciero",
            dataType: FormDataType.MULTI_SELECT_FIELD,
            options: OPTIONS_ITEMS.calificacionSisFinanciero,
            infoField: "Evaluación de estabilidad y riesgo en instituciones financieras para informar decisiones.",
        },
        {
            id: "tipoCliente",
            label: "Tipo Cliente",
            name: "tipoCliente",
            dataType: FormDataType.SELECT_FIELD,
            options: OPTIONS_ITEMS.clientTypes,
            infoField: "Identificación del tipo de cliente en servicios financieros, categorizado según necesidades y comportamientos.",
        },
        {
            id: "refAtraso",
            label: "Referencia de atraso",
            name: "refAtraso",
            dataType: FormDataType.MULTI_SELECT_FIELD,
            options: OPTIONS_ITEMS.refAtraso,
        },
        {
            id: "promedioAcreditacion",
            label: "Promedio de acreditación",
            name: "promedioAcreditacion",
            dataType: FormDataType.CURRENCY_FIELD,
            prefixText: CURRENCY.symbol,
        },
        {
            id: "rangoInforcomf",
            label: "Faja en Informconf",
            name: "rangoInforcomf",
            dataType: FormDataType.TEXT_FIELD,
            infoField: "La Faja de Informconf es un indicador para evaluar la probabilidad de que una persona cumpla con sus deudas.",
            maxLength: 1,
        },
        {
            id: "calificacionBcp",
            label: "Calificación en el BCP",
            name: "calificacionBcp",
            dataType: FormDataType.MULTI_SELECT_FIELD,
            options: OPTIONS_ITEMS.bcpRating,
        },
        {
            id: "antiguedad",
            label: "Antiguedad Laboral (Meses)",
            name: "antiguedad",
            dataType: FormDataType.NUMBER_FIELD,
            infoField: "Antiguedad Laboral",
        },
        {
            id: "inhabilitadoInfocheck",
            label: "Exclusión en Infocheck",
            name: "inhabilitadoInfocheck",
            dataType: FormDataType.SWITCH_FIELD,
        },
        {
            id: "tieneMora",
            label: "¿Tiene Mora?",
            name: "tieneMora",
            dataType: FormDataType.RADIO_FIELD_GROUP,
            options: [
                { label: "Sí", value: "true" },
                { label: "No", value: "false" },
            ],
        },
        {
            id: "poseeCuentaGs",
            label: "¿Posee Cuenta en Guaraníes",
            name: "poseeCuentaGs",
            dataType: FormDataType.RADIO_FIELD_GROUP,
            infoField: "Verifica si el cliente posee cuenta en Guaraníes",
            options: [
                { label: "Sí", value: "true" },
                { label: "No", value: "false" },
            ],
        },
        {
            id: "ALDnegativo",
            label: "Exclusión listas negativas",
            name: "ALDnegativo",
            dataType: FormDataType.SWITCH_FIELD,
            infoField: "Verifica si el cliente tiene listas negativas",
        },
        {
            id: "riesgoOperacionSospechosa",
            label: "Exclusión Riesgo de Operación Sospechosa (ROS)",
            name: "riesgoOperacionSospechosa",
            dataType: FormDataType.SWITCH_FIELD,
        },
        {
            id: "",
            label: "",
            name: "",
            dataType: FormDataType.EMPTY,
            xs: 8,
        },
        {
            id: "exclusionEmpresas",
            label: "Excluir Empresas",
            name: "exclusionEmpresas",
            dataType: FormDataType.LIST_DATA_GRID,
            columns: COLUMNS_DEFS.employeeColumns,
            inputFieldVariant: "single",
            dataGridOptions: {
                counterTextSingular: "Empresa",
                counterTextPlural: "Empresas",
                inputLabel: "Buscar Empresa",
            },
        },
        {
            id: "exclusionSucursales",
            label: "Excluir Sucursales",
            name: "exclusionSucursales",
            dataType: FormDataType.LIST_DATA_GRID,
            columns: COLUMNS_DEFS.branchesColumns,
            inputFieldVariant: "multiple",
            searchService: ESearchServices.BRANCH,
            dataGridOptions: {
                counterTextSingular: "Sucursal",
                counterTextPlural: "Sucursales",
                inputLabel: "Buscar Sucursal",
            },
        },
    ],
}

const grupoCamposEssap: FormGroup<PreApprovedParameters> = {
    id: "1",
    name: "essap",
    label: "ESSAP",
    fields: [
        {
            id: "cantidadPrestamos",
            label: "Cantidad de Préstamos",
            name: "cantidadPrestamos",
            dataType: FormDataType.NUMBER_FIELD,
            infoField: "Si la cantidad de préstamos del cliente es superior a éste, quedará excluido de calificar para pre-aprobado.",
        },
        {
            id: "nivelEndeudamiento",
            label: "Nivel de Endeudamiento",
            name: "nivelEndeudamiento",
            dataType: FormDataType.NUMBER_FIELD,
            prefixText: "%",
            infoField: "Si el porcentaje de endeudamiento del cliente es superior a éste, quedará excluido de calificar para pre-aprobado.",

        },
        {
            id: "edadPrestableCamposEssap",
            label: "Rango de Edad Prestable",
            name: "edadPrestableCamposEssap",
            dataType: FormDataType.RANGE_NUMBER_VALUE,
            rangeOptions: [
                {
                    id: "minimoEdadLimiteEssap",
                    name: "minimoEdadLimiteEssap",
                    label: "Edad Mínima",
                    placeholder: "18",
                    min: 0,
                    max: 100000000,
                    step: 100000,
                    unit: "S/.",
                },
                {
                    id: "maximoEdadLimiteEssap",
                    name: "maximoEdadLimiteEssap",
                    label: "Edad Máxima",
                    placeholder: "60",
                    min: 0,
                    max: 100000000,
                    step: 100000,
                    unit: "S/.",
                },
            ],
        },
        {
            id: "limiteCredito",
            label: "Rango de Monto Prestable",
            name: "limiteCredito",
            dataType: FormDataType.RANGE_CURRENCY_VALUE,
            rangeOptions: [
                {
                    id: "minimoMontoPrestableLimite",
                    name: "minimoMontoPrestableLimite",
                    label: "Monto Mínimo",
                    placeholder: "1.000.000",
                    min: 0,
                    max: 100000000,
                    step: 100000,
                    unit: "S/.",
                },
                {
                    id: "maximoMontoPrestableLimite",
                    name: "maximoMontoPrestableLimite",
                    label: "Monto Máximo",
                    placeholder: "1.000.000.000",
                    min: 0,
                    max: 60,
                    step: 100000,
                    unit: "S/.",
                },
            ],
        },
    ],
}

const grupoCamposGrupoEmpresa: FormGroup<PreApprovedParameters> = {
    id: "1",
    name: "grupoEmpresa",
    label: "EMPRESAS DEL GRUPO",
    fields: [
        {
            id: "nivelEndeudamiento",
            label: "Nivel de Endeudamiento",
            name: "nivelEndeudamientoEmpresasGrupo",
            dataType: FormDataType.NUMBER_FIELD,
            prefixText: "%",
            infoField: "El Nivel de Endeudamiento es el porcentaje de endeudamiento que tiene el cliente",

        },
        {
            id: "cantidadPrestamos",
            label: "Cantidad de Préstamos",
            name: "cantidadPrestamos",
            dataType: FormDataType.NUMBER_FIELD,
            infoField: "Cuenta la cantidad de préstamos que tiene el cliente",
        },
        {
            id: "edadPrestableGrupoEmpresa",
            label: "Rango de Edad Prestable",
            name: "edadPrestableGrupoEmpresa",
            dataType: FormDataType.RANGE_NUMBER_VALUE,
            rangeOptions: [
                {
                    id: "minimoEdadLimite",
                    name: "minimoEdadLimite",
                    label: "Edad Mínima",
                    placeholder: "18",
                    min: 0,
                    max: 60,
                    step: 100000,
                    unit: "S/.",
                },
                {
                    id: "maximoEdadLimite",
                    name: "maximoEdadLimite",
                    label: "Edad Máxima",
                    placeholder: "60",
                    min: 0,
                    max: 60,
                    step: 100000,
                    unit: "S/.",
                },
            ],
        },
    ],
};

const grupoCamposRenovados: FormGroup<PreApprovedParameters> = {
    id: "1",
    name: "renovados",
    label: "RENOVADOS",
    fields: [
        {
            id: "nivelEndeudamiento",
            label: "Nivel de Endeudamiento",
            name: "nivelEndeudamiento",
            dataType: FormDataType.NUMBER_FIELD,
            prefixText: "%",
            infoField: "El Nivel de Endeudamiento es el porcentaje de endeudamiento que tiene el cliente",

        },
        {
            id: "porcentajePagoCredito",
            label: "Amortización del Préstamo",
            name: "porcentajePagoCredito",
            prefixText: "%",
            dataType: FormDataType.NUMBER_FIELD,
            infoField: "Porcentaje de Amortización del Préstamo",
        },
        {
            id: "cantidadPrestamos",
            label: "Cantidad de Préstamos",
            name: "cantidadPrestamos",
            dataType: FormDataType.NUMBER_FIELD,
            infoField: "Cuenta la cantidad de préstamos que tiene el cliente",
        },
        {
            id: "edadPrestableRenovados",
            label: "Rango de Edad Prestable",
            name: "edadPrestableRenovados",
            dataType: FormDataType.RANGE_NUMBER_VALUE,
            rangeOptions: [
                {
                    id: "minimoEdadLimite",
                    name: "minimoEdadLimite",
                    label: "Edad Mínima",
                    placeholder: "18",
                    min: 0,
                    max: 60,
                    step: 1,
                    unit: "S/.",
                },
                {
                    id: "maximoEdadLimite",
                    name: "maximoEdadLimite",
                    label: "Edad Máxima",
                    placeholder: "60",
                    min: 0,
                    max: 60,
                    step: 1,
                    unit: "S/.",
                },
            ],
        },
    ],
};

const grupoCamposContiflashOnline: FormGroup<PreApprovedParameters> = {
    id: "1",
    name: "contiflashOnline",
    label: "CONTIFLASH ONLINE",
    fields: [
        {
            id: "edadPrestableContiflashOnline",
            label: "Rango de Edad Prestable",
            name: "edadPrestableContiflashOnline",
            dataType: FormDataType.RANGE_NUMBER_VALUE,
            rangeOptions: [
                {
                    id: "minimoEdadLimite",
                    name: "minimoEdadLimite",
                    label: "Edad Mínima",
                    placeholder: "18",
                    min: 0,
                    max: 60,
                    step: 1,
                    unit: "S/.",
                },
                {
                    id: "maximoEdadLimite",
                    name: "maximoEdadLimite",
                    label: "Edad Máxima",
                    placeholder: "60",
                    min: 0,
                    max: 60,
                    step: 1,
                    unit: "S/.",
                },
            ],
        },
    ]
};

const formParametersSalarioLayout: FormLayout<PreApprovedParameters> = {
    groups: [
        grupoCamposGenerales,
        grupoCamposEssap,
        grupoCamposGrupoEmpresa,
        grupoCamposRenovados,
        grupoCamposContiflashOnline
    ]
}

export {
    formParametersSalarioLayout
}