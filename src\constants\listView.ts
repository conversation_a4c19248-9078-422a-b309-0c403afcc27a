export enum EListViewEndpoint {
    PRE_APPROBED_CLIENTS = 'preApprovedView',
    PRE_APPROBED_CLIENTS_HISTORY = 'preApprovedHistoryView',
}

export const PAGINATION_LABELS_HISTORICO = [
    { value: 5, label: '5' },
    { value: 10, label: '10' },
    { value: 20, label: '20' },
    { value: 50, label: '50' },
    { value: 100, label: '100' },
    { value: 500, label: '500' },
    { value: 1000, label: '1000' },
];

export const PAGINATION_LABELS_CLIENTES_MES_ACTUAL = [
    { value: 5, label: '5' },
    { value: 10, label: '10' },
    { value: 20, label: '20' },
    { value: 50, label: '50' },
    { value: 100, label: '100' },
    { value: 500, label: '500' },
    { value: 1000, label: '1000' },
    { value: 100000, label: 'Todos' },
];