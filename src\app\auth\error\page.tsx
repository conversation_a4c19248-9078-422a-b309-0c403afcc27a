"use client"
import ErrorAuth from '@/components/_common/ErrorAuth'
import { EAbiType } from '@/interfaces/utils'
import { useSearchParams } from 'next/navigation'
import React from 'react'

const Page = () => {
    const searchParams = useSearchParams();
    const reset = () => {
        location.href = "/";
    }
    const error = searchParams.get("error") ?? "internal_error";
    const msg = searchParams.get("msg") ?? "¡Vaya! Algo no salió bien";
    return (
        <ErrorAuth
            abiType={error == "permission_denied" ? EAbiType.seguridad : EAbiType.triste}
            message={msg}
            buttonOnClick={reset}
        />
    )
}

export default Page