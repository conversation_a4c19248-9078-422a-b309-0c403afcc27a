'use client'
import FormGroupTabs from '@/components/_common/forms/FormGroupTabs';
import { FormGroubTab } from '@/interfaces/forms';
import { PreApprovedParameters } from '@/interfaces/parameters';
import Typography from '@mui/material/Typography';
import { formParametersSalarioLayout } from '../(config)/parametersConfigSalario';
import { useState, useEffect } from 'react';
import notify from '@/helpers/notifyHelper';
import { verificarPermisos } from '@/config/accesos';
import Alert from '@mui/material/Alert';
import CircularProgress from '@mui/material/CircularProgress';
import { FORM_GROUP_TABS_OBJ } from '../(enums)/FormPreAprobadosPgs';
import AutoAuth from '@/app/auth/signin/(components)/AutoAuth';
import { utilHelper } from '@/helpers/utilHelper';
export default function Page() {
    const formGroubTabs: FormGroubTab<PreApprovedParameters>[] = [
        { id: FORM_GROUP_TABS_OBJ.ID, label: FORM_GROUP_TABS_OBJ.LABEL, formLayout: formParametersSalarioLayout }
    ];
    const [visualizarPagina, setVisualizarPagina] = useState(false);
    const [cargandoPermisos, setCargandoPermisos] = useState(true);
    const { status, data } = AutoAuth({ urlRedireccion: '/pre-aprobados/actualizarParametros' });

    useEffect(() => {
        if (status === 'authenticated' && utilHelper.esValido(data?.user?.email)) {
            const verificarPermisoVisualizacion = async () => {
                if (typeof window !== 'undefined') {
                    let tienePermisos = await verificarPermisos(data?.user?.email!);

                    if (!tienePermisos) {
                        notify.error("No tienes permisos para visualizar esta página");
                        setCargandoPermisos(false);
                        setVisualizarPagina(false);
                    } else {
                        setVisualizarPagina(true);
                        setCargandoPermisos(false);
                    }
                } else {
                    setVisualizarPagina(false);
                    setCargandoPermisos(false);
                }
            }
            setTimeout(() => {
                verificarPermisoVisualizacion();
            }, 5000)
        }
    }, [status == 'authenticated', data]);

    return (
        <>
            {cargandoPermisos ? (
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                    <Alert severity="info" icon={false} style={{ marginTop: '12px', fontWeight: 'bold', fontSize: '17px', borderRadius: '12px', display: 'flex', alignItems: 'center', padding: '16px' }}>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                            <CircularProgress size={24} style={{ marginRight: '8px' }} />
                            <span>Consultando permisos...</span>
                        </div>
                    </Alert>
                </div>
            ) : (
                <>
                    {visualizarPagina ? (
                        <>
                            <Typography variant="h5">
                                PARAMETROS PRE-APROBADOS
                            </Typography>
                            <FormGroupTabs tabs={formGroubTabs} />
                        </>
                    ) : (
                        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                            <Alert severity="error" style={{ marginTop: '12px', fontWeight: 'bold', fontSize: '17px', borderRadius: '12px' }}>
                                No tiene permisos para visualizar esta página.
                            </Alert>
                        </div>
                    )}
                </>
            )}
        </>
    );
};