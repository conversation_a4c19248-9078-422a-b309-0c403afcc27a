import WarningIcon from '@mui/icons-material/Warning';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import CustomModal from './CustomModal';

interface ConfirmationActionsProps {
    onConfirm: () => void;
    onCancel: () => void;
    confirmText?: string;
    cancelText?: string;
}
const ConfirmationActions = ({ onConfirm, onCancel, confirmText, cancelText }: ConfirmationActionsProps) => {
    return (
        <Box sx={{ display: "flex", justifyContent: "center", width: "100%", gap: 3 }}>
            <Button variant="contained" color="inherit" onClick={onCancel}>
                {cancelText ?? "Cancelar"}
            </Button>
            <Button variant="contained" color="error" onClick={onConfirm}>
                {confirmText ?? "Confirmar"}
            </Button>
        </Box>
    )
}

const ConfirmationContent = ({ confirmationText }: { confirmationText: string }) => {
    return (
        <Box sx={{ pl: 7, pr: 7 }}>
            <Typography variant="subtitle1" textAlign={"center"} sx={{ color: "CaptionText" }}>
                {confirmationText}
            </Typography>
        </Box>
    )
}

interface ConfirmationModalProps {
    open: boolean;
    onClose: () => void;
    confirmationText: string;
    onConfirm: () => void;
    onCancel: () => void;
    confirmText?: string;
    cancelText?: string;
}
const ConfirmationModal = ({
    open,
    onClose,
    confirmationText,
    onConfirm,
    onCancel,
    confirmText,
    cancelText,
}: ConfirmationModalProps) => {
    return (
        <CustomModal
            open={open}
            onClose={onClose}
            dialogTitleComponent={"div"}
            renderTitle={() => <>
                <WarningIcon color="error" fontSize="large" sx={{ fontSize: "85px" }} />
            </>
            }
            dialogTitleSxProps={{
                padding: "16px 0px 0px 0px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
            }}

            dialogActionsSxProps={{
                pb: 3,
                pr: 0,
                pl: 0,
                pt: 1,
            }}

            dialogContentSxProps={{
                p: 3,
            }}

            sx={{ minWidth: 550, minHeight: 250 }}
            content={<>
                <ConfirmationContent confirmationText={confirmationText} />
            </>}
            actions={
                <ConfirmationActions
                    onConfirm={onConfirm}
                    onCancel={onCancel}
                    confirmText={confirmText}
                    cancelText={cancelText}
                />
            }

            disableBackdropClick={true}
            disableEscapeKeyDown={true}
        />
    )
}

export default ConfirmationModal