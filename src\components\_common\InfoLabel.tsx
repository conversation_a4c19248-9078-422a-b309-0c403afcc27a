import ClearIcon from '@mui/icons-material/Clear';
import { IconButton } from '@mui/material';
import Box from '@mui/material/Box';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { SxProps, Theme, styled } from '@mui/material/styles';
import React from 'react';

const ClearIconButtonStyled = styled(IconButton)(({ theme }) => ({
    '&:hover': {
        backgroundColor: theme.palette.grey[400],
    },
}));

interface InfoLabelProps {
    label: string;
    value: string;
    variant?: "text" | "input-read-only";
    sx?: SxProps<Theme>;
    labelPropsSx?: SxProps<Theme>;
    valuePropsSx?: SxProps<Theme>;
    ref?: React.RefObject<HTMLInputElement>;
    clearable?: boolean;
    onClear?: () => void;
}
const InfoLabel = ({ label, value, variant = "text", sx, labelPropsSx, valuePropsSx, ref, clearable = false, onClear }: InfoLabelProps) => {
    const handleClear = () => {
        onClear && onClear();
    }
    return (
        <Box sx={{
            display: "flex",
            gap: 1,
            alignItems: "center",
            marginBottom: "10px",
            ...sx
        }}>
            <Typography
                fontWeight={"bold"}
                sx={{ ...labelPropsSx }}
            >
                {label}:
            </Typography>
            {variant == "text" && <Typography>{value}</Typography>}
            {
                variant == "input-read-only" &&
                <TextField
                    ref={ref}
                    size='small'
                    variant='standard'
                    value={value}
                    disabled
                    sx={{
                        backgroundColor: "#e0e0e0",
                        borderRadius: "5px",
                        width: "200px",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        "& .MuiInputBase-root": {
                            "&:before": {
                                borderBottom: "none !important"
                            },
                            "& .Mui-disabled": {
                                color: "black !important"
                            },
                        },
                        ...valuePropsSx
                    }}
                    inputProps={{
                        sx: {
                            padding: "4px 5px 5px", 
                            width: "100%",
                            overflow: "auto",
                            textOverflow: "ellipsis",
                            WebkitTextFillColor: "black !important",
                        },
                    }}
                    InputProps={{
                        endAdornment: clearable && (
                            <InputAdornment position='start'>
                                <ClearIconButtonStyled onClick={handleClear} size="small">
                                    <ClearIcon fontSize="small" />
                                </ClearIconButtonStyled>
                            </InputAdornment>
                        )
                    }}
                />
            }
        </Box>
    )
}

export default InfoLabel