import { preAprobadosApi } from "@/lib/api";
import { UpdateAccionesUsuario } from "@/models/responses/usuariosServices";
import { usuarioServices } from "@/services/usuarioServices";
import axios from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    data: string
    error?: string
    status: number
    meta?: any
}
export async function POST(req: NextRequest) {
    let responseBody: Response = {
        data: '',
        error: "Error al procesar la solicitud de actualización de acciones del usuario",
        status: 200,
        meta: undefined
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 401;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    preAprobadosApi.defaults.nextRequest = req;

    const body = await req.json() as { request: UpdateAccionesUsuario };

    const request = body.request;

    try {
        const resp = await usuarioServices.updateAccionesUsuario(request);
        responseBody.data = resp.data;
        return NextResponse.json(responseBody.data, { status: 200 });
    } catch (error: any) {
        responseBody.error = "Error al actualizar las acciones del usuario";
        responseBody.status = 500;
        if (axios.isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
            responseBody.meta = {
                data: error.config?.data,
                url: error.config?.url,
            };
        }
        console.error(error);
        return NextResponse.json(responseBody, { status: responseBody.status });
    }


}