import axios from "axios";
import { getToken } from "next-auth/jwt";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest, res: NextResponse) {
    try {
        const token = await getToken({ req });

        const urlBase = `${process.env.NEXT_PUBLIC_KEYCLOAK_ISSUER}/protocol/openid-connect`;
        const logoutUrl = "/logout";

        const searchParams = new URLSearchParams();
        searchParams.append("post_logout_redirect_uri", process.env.NEXTAUTH_URL ?? "");
        searchParams.append("id_token_hint", token?.idToken ?? "");
        searchParams.append("client_id", process.env.NEXT_PUBLIC_KEYCLOAK_ID ?? "");

        const resp = await axios.get(`${urlBase}${logoutUrl}?${searchParams.toString()}`);
        return NextResponse.json("OK", { status: 200 });
    } catch (error) {
        console.error("Error during logout:", error);
        return NextResponse.json({ message: "Internal server error" }, { status: 500 });
    }
}