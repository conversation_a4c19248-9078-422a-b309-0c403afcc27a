import { SucursalModel } from "@/models/responses/authServices";
import { ClientModel } from "@/models/responses/clientServices";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

export interface UtilStore {
    branches: SucursalModel[];
    setBranches: (branches: SucursalModel[]) => void;

    clients: ClientModel[];
    setClients: (clients: ClientModel[]) => void;
};

export const useUtilStore = create(persist<UtilStore>(
    (set) => ({
        branches: [],
        setBranches: (branches: SucursalModel[]) => set({ branches }),

        clients: [],
        setClients: (clients: ClientModel[]) => set({ clients }),
    }),
    {
        name: "util-storage",
        storage: createJSONStorage(() => sessionStorage),
    }
));

export const getBranchesOutside = () => {
    return useUtilStore.getState().branches;
}

export const getClientsOutside = () => {
    return useUtilStore.getState().clients;
}