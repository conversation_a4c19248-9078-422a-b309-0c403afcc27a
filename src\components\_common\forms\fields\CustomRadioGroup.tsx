"use client"
import { useInfoFieldPopper } from '@/hooks/useInfoFieldPopper';
import { useStatusFieldPopper } from '@/hooks/useStatusFieldPopper';
import { OptionItem, TValues } from '@/interfaces/_common';
import { PreApprovedParametersGroup } from '@/interfaces/parameters';
import { useFieldMetadataByFieldNameFormState, useFormStore } from '@/stores/formStore';
import { ErrorMessage } from '@hookform/error-message';
import { FormControlLabel, FormGroup, FormLabel, Radio, RadioGroup } from '@mui/material';
import { useEffect } from 'react';
import { Controller, get, useFormContext } from 'react-hook-form';
import FormValidationMessage from '../FormValidationMessage';
import InfoFieldIcon from '../InfoFieldIcon';
import InfoFieldPopper from '../InfoFieldPopper';
import StatusFieldIcon from '../StatusFieldIcon';
import StatusFieldPopper from '../StatusFieldPopper';

interface CustomCheckBoxProps<TData extends TValues> {
    id: string;
    name: keyof TData;
    label: string;
    groupId: string;
    options: OptionItem[];
    info?: string;
}
export const CustomRadioGroup = <TData extends TValues>({ label, name, options, groupId, info }: CustomCheckBoxProps<TData>) => {
    const { formState: { errors }, getValues } = useFormContext();

    const grupos = getValues("grupos") as PreApprovedParametersGroup[];
    const index = grupos.findIndex((grupo) => grupo.id === groupId);
    const name_ = `grupos.${index}.parameters.${name as string}`; 3
    const error_ = get(errors, name_);

    const { handleClickInfo,
        anchorEl,
        open,
        id,
        handleClickAway
    } = useInfoFieldPopper(name_);

    const {
        handleClickInfo: handleClickStatus,
        anchorEl: anchorElStatus,
        open: openStatus,
        id: idStatus,
        handleClickAway: handleClickAwayStatus
    } = useStatusFieldPopper(name as string + "status");

    const addError = useFormStore((state) => state.addError);
    const removeErrors = useFormStore((state) => state.removeError);
    const fieldMetadata = useFieldMetadataByFieldNameFormState(name as string);
    const fieldMetadataStatus = fieldMetadata?.state ?? "approved";
    useEffect(() => {
        const key = name_ as string;
        if (error_) {
            const message = error_.message as string ?? "";
            addError({ groupId, field: key, message });
        } else {
            removeErrors({ groupId, field: key });
        }
    }, [errors]);

    return (
        <FormGroup>
            <FormLabel sx={{ display: "flex", alignItems: "center" }}>
                {label}
                {info && <InfoFieldIcon sx={{ marginLeft: "2px" }} onClick={handleClickInfo} />}
                {fieldMetadata && <StatusFieldIcon sx={{ marginLeft: "2px" }} onClick={handleClickStatus} status={fieldMetadata.state} />}
            </FormLabel>
            <Controller
                render={({ field }) => (
                    <RadioGroup aria-label={label.toLowerCase()} {...field} row>
                        {
                            options.map((option, index) => (
                                <FormControlLabel
                                    key={index}
                                    value={option.value}
                                    control={<Radio />}
                                    label={option.label}
                                    disabled={fieldMetadataStatus == "pending"}
                                />
                            ))
                        }
                    </RadioGroup>
                )}
                name={name_}
            />
            <ErrorMessage
                name={name_}
                errors={errors}
                render={({ message }) => <FormValidationMessage message={message} />}
            />
            {info && (
                <InfoFieldPopper
                    id={id}
                    open={open}
                    anchorEl={anchorEl}
                    info={info}
                    handleClickAway={handleClickAway}
                />
            )}

            {fieldMetadata && (
                <StatusFieldPopper
                    id={idStatus}
                    status={fieldMetadata.state}
                    open={openStatus}
                    anchorEl={anchorElStatus}
                    handleClickAway={handleClickAwayStatus}
                />
            )}
        </FormGroup>


    );
}