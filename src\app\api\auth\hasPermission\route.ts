import { authHelper } from "@/helpers/authHelper";
import { baseApi } from "@/lib/api";
import { authServices } from "@/services/authServices";
import { isAxiosError } from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    authorized: boolean,
    error?: string
    status: number
    meta?: any
}
export async function GET(req: NextRequest) {
    let responseBody: Response = {
        authorized: false,
        error: undefined,
        status: 200
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 404;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    baseApi.defaults.nextRequest = req;

    const userCode = req.nextUrl.searchParams.get("userCode") ?? "";
    try {
        const { data: permissions } = await authServices.getAuthUserPermissions(userCode);
        const hasPermission = authHelper.hasPermission(permissions);

        if (hasPermission) {
            return NextResponse.json(true, { status: 200 })
        } else {
            return NextResponse.json(false, { status: 401 })
        }

    } catch (error) {
        console.error(error);
        responseBody.error = "Error al obtener los permisos del cliente";
        responseBody.status = 500;
        responseBody.meta = error;
        if (isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
            responseBody.meta = {
                data: error.config?.data,
                url: error.config?.url,
            };
        }
        return NextResponse.json(responseBody, { status: responseBody.status });
    }

}