import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import CustomCircularProgress from '../CustomCircularProgress';
import CustomModal from './CustomModal';

interface SubmitActionsProps {
    onClickActionButton: () => void;
    errorBtnText?: string;
    successBtnText?: string;
    error?: boolean;
    success?: boolean;
}
const SubmitActions = ({
    onClickActionButton,
    errorBtnText = "Aceptar",
    successBtnText = "Aceptar",
    error,
    success
}: SubmitActionsProps) => {
    return (
        <Box sx={{ display: "flex", justifyContent: "center", width: "100%" }}>
            <Button variant="contained" color="primary" onClick={onClickActionButton}>
                {error && errorBtnText}
                {!error && success && successBtnText}
            </Button>
        </Box>
    )
}

const SubmitContent = ({ text }: { text: string }) => {
    return (
        <Box sx={{ pl: 7, pr: 7 }}>
            <Typography variant="subtitle1" textAlign={"center"} sx={{ color: "CaptionText" }}>
                {text}
            </Typography>
        </Box>
    )
}

interface SubmitModalProps {
    open: boolean;
    onClose: () => void;
    text: string;
    onClickActionButton: () => void;
    errorBtnText?: string;
    successBtnText?: string;
    isLoading?: boolean;
    error?: boolean;
    success?: boolean;
}
const SubmitModal = ({
    open,
    onClose,
    text,
    onClickActionButton,
    errorBtnText,
    successBtnText,
    isLoading = true,
    error = false,
    success = true,
}: SubmitModalProps) => {
    return (
        <CustomModal
            open={open}
            onClose={onClose}
            dialogTitleComponent={"div"}
            renderTitle={() => <>
                {!isLoading && (<>
                    {error && <ErrorIcon color="error" fontSize="large" sx={{ fontSize: "85px" }} />}
                    {!error && success && <CheckCircleIcon color="success" fontSize="large" sx={{ fontSize: "85px" }} />}
                </>)}
            </>
            }
            dialogTitleSxProps={{
                padding: !isLoading ? "16px 0px 0px 0px" : 0,
                display: "flex",
                flex: !isLoading ? "1 1 auto" : 1,
                justifyContent: "center",
                alignItems: "center",
            }}

            dialogActionsSxProps={{
                pb: !isLoading ? 3 : 0,
                pr: 0,
                pl: 0,
                pt: !isLoading ? 1 : 0,
            }}

            dialogContentSxProps={{
                p: !isLoading ? 3 : 0,
            }}

            sx={{ minWidth: 550, minHeight: 250 }}
            content={<>
                {!isLoading && <SubmitContent text={text} />}
                {isLoading && (
                    <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100%" }}>
                        <CustomCircularProgress size={"65px"} />
                    </Box>
                )}
            </>}
            actions={
                <>
                    {!isLoading && (
                        <SubmitActions
                            error={error}
                            success={success}
                            onClickActionButton={onClickActionButton}
                            errorBtnText={errorBtnText}
                            successBtnText={successBtnText}
                        />
                    )}
                </>
            }

            disableBackdropClick={isLoading}
            disableEscapeKeyDown={isLoading}
            disableCloseButton={isLoading}
        />
    )
}

export default SubmitModal