import { TValues } from "./_common";

export interface FormLinkWithId<EntryValue extends TValues = any, OutputValue extends TValues = any> {
    id: string;
    name: keyof OutputValue;
    parameterName: string;
    toOutputValue: (value: EntryValue) => any;
    toEntryValue: (value: OutputValue) => any;
    isModified?: (value: OutputValue, oldValue: OutputValue) => boolean;
}

export enum EAbiType {
    feliz = "feliz",
    interrogacion = "interrogacion",
    saludando = "saludando",
    seguridad = "seguridad",
    triste = "triste",
}

export interface ErrorAuthUI {
    status: boolean;
    message: string;
    subMessage?: string;
    description?: string;
    abiType: EAbiType;
}