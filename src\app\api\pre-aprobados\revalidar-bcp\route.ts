import { baseApi, preAprobadosApi } from "@/lib/api";
import { preApprovedService } from "@/services/preApprovedService";
import axios from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    revalidate: boolean,
    error?: string
    status: number
    meta?: any
}
export async function PUT(req: NextRequest) {
    let responseBody: Response = {
        revalidate: false,
        error: undefined,
        status: 200
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 401;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    preAprobadosApi.defaults.nextRequest = req;

    const body = await req.json() as { datosPreAprobados: any[] };
    const datosPreAprobados = body.datosPreAprobados;

    try {
        const resp = await preApprovedService.revalidateBcpPreApprovedClients(datosPreAprobados);
        responseBody.revalidate = resp.data;
        return NextResponse.json(responseBody.revalidate, { status: 200 });
    } catch (error: any) {
        responseBody.error = "Error al revalidar los datos";
        responseBody.status = 500;
        responseBody.meta = error;
        if (axios.isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
            responseBody.meta = {
                data: error.config?.data,
                url: error.config?.url,
            };
        }
        console.error(error);
        return NextResponse.json(responseBody, { status: responseBody.status });
    }

}