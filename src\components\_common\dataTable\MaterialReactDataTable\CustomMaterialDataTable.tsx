"use client"
import { useWindowSize } from '@/hooks/useWindowsSize';
import { SimpleRowSelectionState } from '@/interfaces/_common';
import { Box, Stack } from '@mui/material';
import { ColumnDef, Updater } from "@tanstack/react-table";
import {
    MRT_Cell,
    MRT_ColumnDef,
    MRT_Row,
    MRT_RowData,
    MRT_TableContainer,
    MRT_TableHeadCellFilterContainer,
    MRT_TableInstance,
    MRT_TableOptions,
    MRT_TablePagination,
    MRT_TablePaper,
    MaterialReactTable,
    useMaterialReactTable
} from 'material-react-table';
import { MRT_Localization_ES } from 'material-react-table/locales/es';
import React, { memo, useRef } from 'react';
import CustomFilterContainer from '../../CustomFilterContainer';
import SimpleDataTable, { DataTableProps } from './SimpleDataTable';

export interface NestedRowData<TData extends MRT_RowData> {
    subRows: TData[];
    data: TData;
}

export type OnChangeNestedFn<T, TData extends MRT_RowData> = (updaterOrValue: Updater<T>, parentRow: MRT_Row<TData>, table: MRT_TableInstance<TData>) => void;

export interface CustomMaterialDataTableProps<TData extends MRT_RowData = any> extends MRT_TableOptions<TData> {
    columns: MRT_ColumnDef<TData>[];
    data: TData[];
    isLoading?: boolean;
    actions?: (props: {
        cell: MRT_Cell<TData>;
        row: MRT_Row<TData>;
        table: MRT_TableInstance<TData>;
    }) => React.ReactNode;
    variant?: "normal" | "compact";
    enableNestedTable?: boolean;
    nestedFields?: ColumnDef<TData>[];
    onSubRowSelectionChange?: OnChangeNestedFn<SimpleRowSelectionState, TData>;
    subRowSelection?: SimpleRowSelectionState;
    nestedTableProps?: Partial<DataTableProps<TData>>;
    pixelsPaperHeightToSubstract?: number;
    pixelsContainerHeightToSubstract?: number;
    pixelsWidthToSubstract?: number;
    sideFilterOpened?: boolean;
}

const CustomMaterialDataTable = memo(function CustomMaterialDataTable<TData extends MRT_RowData>({
    columns,
    data,
    isLoading = false,
    actions,
    variant = "normal",
    enableSelectAll = false,
    enableRowSelection = false,
    onRowSelectionChange,
    enableNestedTable,
    nestedFields = [],
    onSubRowSelectionChange,
    subRowSelection,
    nestedTableProps,
    pixelsPaperHeightToSubstract,
    pixelsContainerHeightToSubstract,
    pixelsWidthToSubstract = 0,
    columnFilterDisplayMode,
    sideFilterOpened = false,
    ...props
}: CustomMaterialDataTableProps<TData>) {
    const padreRef = useRef(null);
    const containerRef = useRef(null);
    const { getRestantHeight } = useWindowSize(padreRef);
    const { getRestantHeight: getRestantHeightContainer } = useWindowSize(containerRef);

    const renderDetailPanel = enableNestedTable ? ({ row }: {
        row: MRT_Row<TData>;
        table: MRT_TableInstance<TData>;
    }) => {
        return (
            <SimpleDataTable
                columns={nestedFields}
                data={row.original.subRows} //TIENE QUE SER UN ARRAY DE OBJETOS 
                enableRowSelection={!!enableRowSelection}
                onRowSelectionChange={(updaterOrValue) => {
                    if (onSubRowSelectionChange) {
                        onSubRowSelectionChange(updaterOrValue, row, table);
                    }
                }}
                rowSelection={subRowSelection}
                variant={"nested"}
                {...nestedTableProps}
            />
        )
    } : undefined;

    const table = useMaterialReactTable({
        //REQUIRED
        columns,
        data,

        //STATE
        state: {
            showLoadingOverlay: isLoading,
        },

        //LAYOUT
        layoutMode: variant == "normal" ? "semantic" : "grid",

        //ACTIONS
        enableRowActions: actions ? true : false,
        renderRowActions: actions,
        enableColumnActions: false,

        //SORTING
        enableSorting: false,

        //PAGINATION
        enablePagination: false,

        //FILTERS
        enableFilters: false,
        filterFns: {
            rangeSliderFilter: (row, id, filterValue, addMeta) => {
                const value = row.getValue(id);
                if (typeof value === "number" || typeof value === "string") {
                    const value_ = Number(value);
                    return value_ >= filterValue[0] && value_ <= filterValue[1] ? true : false;
                }
                return false;
            },
        },

        //TOOLBARS
        enableTopToolbar: false,

        //SELECTABLE
        enableSelectAll: enableSelectAll,
        enableRowSelection: enableRowSelection,
        onRowSelectionChange: onRowSelectionChange,

        //DETAIL PANEL
        renderDetailPanel: renderDetailPanel,

        //LOCALIZATION
        localization: MRT_Localization_ES,


        //MUI COMPONENTS
        muiTablePaperProps: {
            elevation: 2,
            sx: () => ({
                height: pixelsPaperHeightToSubstract !== undefined ? getRestantHeight(168, pixelsPaperHeightToSubstract) : "100%",
                width: !sideFilterOpened ? "100%" : "calc(100% - 300px)",
                transition: "none",
            }),
            ref: padreRef
        },

        muiTableContainerProps: {
            sx: {
                height: pixelsContainerHeightToSubstract !== undefined ? getRestantHeightContainer(168, pixelsContainerHeightToSubstract) : getRestantHeightContainer(168, 7),
                transition: "none",
            },
            ref: containerRef
        },
        muiFilterTextFieldProps: ({ column }) => ({
            label: column.columnDef.filterVariant !== "multi-select" && column.columnDef.filterVariant !== "select"
                ? `${column.columnDef.header}` : "",
            size: "small",
            variant: "outlined",
            sx: {
                minWidth: 0,
            }
        }),
        muiFilterSliderProps: ({ column }) => ({
            size: "small",
        }),

        ...props
    });

    return (
        <>
            {columnFilterDisplayMode != "custom" && <MaterialReactTable table={table} />}
            {columnFilterDisplayMode == "custom" && (
                <Stack direction={"row"} spacing={2} sx={{ width: "100%" }}>
                    {sideFilterOpened && (
                        <CustomFilterContainer table={table}>
                            {table.getLeafHeaders()
                                .filter(header => header.column.columnDef.accessorKey !== undefined)
                                .map((header) => (
                                    <MRT_TableHeadCellFilterContainer
                                        component={Box}
                                        key={header.id}
                                        header={header}
                                        table={table}
                                        sx={{
                                            minHeight: "auto !important",
                                        }}
                                        in
                                    />
                                ))}
                        </CustomFilterContainer>
                    )}

                    <MRT_TablePaper table={table}>
                        {props.positionPagination == "top" && <MRT_TablePagination position="top" table={table} />}
                        <MRT_TableContainer table={table} />
                        {props.positionPagination == "bottom" && <MRT_TablePagination position="bottom" table={table} />}
                    </MRT_TablePaper>
                </Stack>
            )}

        </>
    )
}) as <TData extends MRT_RowData>(props: CustomMaterialDataTableProps<TData>) => JSX.Element;

export default CustomMaterialDataTable