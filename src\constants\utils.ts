import { OptionItem, OptionListGridItem } from "@/interfaces/_common";
import { MRT_ColumnDef } from "material-react-table";

const employeeColumns: MRT_ColumnDef<OptionListGridItem>[] = [
    {
        header: "ID",
        accessorKey: "value",
    },
    {
        header: "Nombre Empresa",
        accessorKey: "label",
    },
];

const branchesColumns: MRT_ColumnDef<OptionListGridItem>[] = [
    {
        header: "ID",
        accessorKey: "value",
    },
    {
        header: "Nombre de Sucursal",
        accessorKey: "label",
    },
];

const systemRatingOptions: OptionItem[] = [
    { label: "Sin Datos", value: "0" },
    { label: "1", value: "1" },
    { label: "2", value: "2" },
    { label: "3", value: "3" },
    { label: "4", value: "4" },
    { label: "5", value: "5" },
];

const delayReferenceOptions: OptionItem[] = [
    { label: "REGULAR", value: "REGULAR" },
    { label: "SIN DATO", value: "SIN DATOS" },
    { label: "MALO", value: "MALO" },
    { label: "EXCELENTE", value: "EXCELENTE" },
    { label: "BUENO", value: "BUENO" },
    { label: "MUY BUENO", value: "MUY BUENO" },
];

const bcpRatingOptions: OptionItem[] = [
    { label: "Sin Datos", value: "0" },
    { label: "1", value: "1" },
    { label: "2", value: "2" },
    { label: "3", value: "3" },
    { label: "4", value: "4" },
    { label: "5", value: "5" },
];

const clientTypesOptions: OptionItem[] = [
    { label: "Físico", value: "F" },
    { label: "Jurídico", value: "J" },
];

export const OPTIONS_ITEMS = {
    calificacionSisFinanciero: systemRatingOptions,
    refAtraso: delayReferenceOptions,
    bcpRating: bcpRatingOptions,
    clientTypes: clientTypesOptions,
};


export const COLUMNS_DEFS = {
    employeeColumns,
    branchesColumns,
};

export enum ESearchServices {
    COMPANY = "company",
    BRANCH = "branch",
}