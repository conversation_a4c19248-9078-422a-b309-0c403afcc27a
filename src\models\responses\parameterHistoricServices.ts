export interface ParametersHistoricModel {
    idHistoricoParametros: string;
    nombreParametro: string;
    tipoDato: string;
    estado: string;
    tipoValor: string;
    valorDesde: string | null;
    valorHasta: string | null;
    valorDesdePendiente: string | null,
    valorHastaPendiente: string | null,
    listaValores: string | null;
    idGrupo: string;
    idHistoricoRegla: string;
    fechaModificacion: string;
    usuarioModificacion: string;
    periodoMes: string;
    PeriodoAnio: string;
}