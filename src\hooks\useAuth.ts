"use client"
import { AUTH_ERROR_ROUTE } from "@/constants/auth";
import { authHelper } from "@/helpers/authHelper";
import { useAuthStore } from "@/stores/authStore";
import { usePathname } from "next/navigation";
import { useEffect } from "react";
import { useQueryAuthApim } from "./queries/useQueryAuthApim";

const expiresIn = 250;
const useAuth = () => {
    const pathname = usePathname();
    const pathnameCleanSearchQuery = pathname.split("?")[0];
    const errorPage = pathnameCleanSearchQuery === AUTH_ERROR_ROUTE;

    const setAuth = useAuthStore(state => state.setAuth);
    const permissions = useAuthStore(state => state.permissions);
    const { data } = useQueryAuthApim(expiresIn);

    useEffect(() => {
        if (data) {
            const auth = data?.data;
            setAuth(auth);
        }
    }, [data]);

    useEffect(() => {
        if (permissions && permissions.length > 0 && !errorPage) {
            const hasPermissions = authHelper.hasPermission(permissions);
            if (!hasPermissions) {
                const params = new URLSearchParams();
                params.append('error', "permission_denied");
                params.append('msg', "No tiene permisos para acceder a esta página");
                const search = params.toString();
                window.location.href = `${AUTH_ERROR_ROUTE}?${search}`;
            }
        }
    }, [permissions]);
}
export {
    useAuth
};