import { authOptions } from "@/auth/auth";
import { getToken } from "next-auth/jwt";
import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest, res: NextResponse) {
    const token = await getToken({ req, secret: authOptions.secret });
    const accessToken = token?.accessToken; // The token from the request
    return NextResponse.json(accessToken, { status: 200 })
}