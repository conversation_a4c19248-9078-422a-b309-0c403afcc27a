import { useInfoFieldPopper } from '@/hooks/useInfoFieldPopper';
import { useStatusFieldPopper } from '@/hooks/useStatusFieldPopper';
import { OptionItem, TValues } from '@/interfaces/_common';
import { PreApprovedParametersGroup } from '@/interfaces/parameters';
import { useFieldMetadataByFieldNameFormState, useFormStore } from '@/stores/formStore';
import { ErrorMessage } from '@hookform/error-message';
import { InputAdornment, SxProps, Theme } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import { useEffect } from 'react';
import { Controller, get, useFormContext } from 'react-hook-form';
import FormValidationMessage from '../FormValidationMessage';
import InfoFieldIcon from '../InfoFieldIcon';
import InfoFieldPopper from '../InfoFieldPopper';
import StatusFieldIcon from '../StatusFieldIcon';
import <PERSON><PERSON>ieldPopper from '../StatusFieldPopper';

interface CustomMultiSelectProps<TData extends TValues> {
    id: string;
    name: keyof TData;
    label: string;
    options: OptionItem[];
    groupId: string;
    optionSelector: keyof OptionItem;
    placeholder?: string;
    shouldUnregister?: boolean;
    required?: boolean;
    sx?: SxProps<Theme>;
    info?: string;
}
const CustomSelect = <TData extends TValues>(props: CustomMultiSelectProps<TData>) => {
    const {
        id,
        name,
        label,
        options,
        placeholder,
        shouldUnregister,
        info,
        groupId,
        sx
    } = props;
    const { formState: { errors }, getValues } = useFormContext();

    const grupos = getValues("grupos") as PreApprovedParametersGroup[];
    const index = grupos.findIndex((grupo) => grupo.id === groupId);
    const name_ = `grupos.${index}.parameters.${name as string}`;
    const error_ = get(errors, name_);

    const {
        handleClickInfo,
        anchorEl,
        open,
        id: idPopper,
        handleClickAway
    } = useInfoFieldPopper(name_);

    const {
        handleClickInfo: handleClickStatus,
        anchorEl: anchorElStatus,
        open: openStatus,
        id: idStatus,
        handleClickAway: handleClickAwayStatus
    } = useStatusFieldPopper(name as string + "status");

    const addError = useFormStore((state) => state.addError);
    const removeErrors = useFormStore((state) => state.removeError);
    const fieldMetadata = useFieldMetadataByFieldNameFormState(name as string);
    const fieldMetadataStatus = fieldMetadata?.state ?? "approved";

    useEffect(() => {
        const key = name_ as string;
        if (error_) {
            const message = error_.message as string ?? "";
            addError({ groupId, field: key, message });
        } else {
            removeErrors({ groupId, field: key });
        }
    }, [errors]);

    return (
        <>
            <Controller
                name={name_}
                shouldUnregister={shouldUnregister}
                render={({ field, formState }) => {
                    return (
                        <Autocomplete<OptionItem, false, true, false>
                            sx={sx}
                            id={id}
                            disableClearable
                            disabled={fieldMetadataStatus == "pending"}
                            options={options}
                            getOptionLabel={(option) => typeof option === 'string' ? option : option.label}
                            isOptionEqualToValue={(option, value) => option.value === value.value}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    label={label}
                                    placeholder={placeholder}
                                    error={error_ ? true : false}
                                    helperText={
                                        <ErrorMessage
                                            name={name_}
                                            errors={errors}
                                            render={({ message }) => <FormValidationMessage message={message} />}
                                        />
                                    }
                                    InputProps={{
                                        ...params.InputProps,
                                        endAdornment: info && !fieldMetadata ? (
                                            <>
                                                {params.InputProps.endAdornment}
                                                <InputAdornment position="start">
                                                    <InfoFieldIcon onClick={handleClickInfo} />
                                                </InputAdornment>
                                            </>
                                        ) : (
                                            <InputAdornment position="end">
                                                {params.InputProps.endAdornment}
                                                {info && <InfoFieldIcon onClick={handleClickInfo} />}
                                                {fieldMetadata && <StatusFieldIcon onClick={handleClickStatus} status={fieldMetadata.state} />}
                                            </InputAdornment>
                                        )
                                    }}

                                    sx={{
                                        width: "100%",
                                        "& .MuiOutlinedInput-root": {
                                            "& fieldset": {
                                                borderColor: get(formState.dirtyFields, name_) ? "orange" : "rgba(0, 0, 0, 0.23)",
                                            }
                                        },
                                        "& .MuiDisabled": {
                                            "& fieldset": {
                                                borderColor: get(formState.dirtyFields, name_) ? "orange" : "rgba(0, 0, 0, 0.23)",
                                            }
                                        }
                                    }}
                                />
                            )}
                            onChange={(event, value, reason, details) => {
                                field.onChange(value);
                            }}
                            value={field.value ?? ""}
                            noOptionsText="No hay opciones"
                        />
                    )
                }}

            />
            {info && (
                <InfoFieldPopper
                    id={id}
                    open={open}
                    anchorEl={anchorEl}
                    info={info}
                    handleClickAway={handleClickAway}
                />
            )}

            {fieldMetadata && (
                <StatusFieldPopper
                    id={idStatus}
                    status={fieldMetadata.state}
                    open={openStatus}
                    anchorEl={anchorElStatus}
                    handleClickAway={handleClickAwayStatus}
                />
            )}
        </>

    )
}

export default CustomSelect