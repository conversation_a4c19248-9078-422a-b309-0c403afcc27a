import CloseIcon from '@mui/icons-material/Close'
import { Breakpoint, IconButton, SxProps, Theme } from "@mui/material"
import Dialog from "@mui/material/Dialog"
import DialogActions from "@mui/material/DialogActions"
import DialogContent from "@mui/material/DialogContent"
import DialogTitle from "@mui/material/DialogTitle"

interface ModaProps {
    open: boolean;
    onClose: () => void;
    title?: string;
    renderTitle?: () => JSX.Element;
        content: JSX.Element;
    actions?: JSX.Element;
    maxWidth?: Breakpoint;
    fullWidth?: boolean;
    scroll?: "paper" | "body";
    fullScreen?: boolean;
    sx?: SxProps<Theme>;
    dialogTitleSxProps?: SxProps<Theme>;
    dialogActionsSxProps?: SxProps<Theme>;
    dialogContentSxProps?: SxProps<Theme>;

    disableBackdropClick?: boolean;
    disableEscapeKeyDown?: boolean;
    disableCloseButton?: boolean;

    hideBackdrop?: boolean;

    dialogTitleComponent?: React.ElementType<any>;
}
const CustomModal = ({
    open,
    onClose,
    title,
    content,
    actions,
    maxWidth = "md",
    fullWidth = false,
    renderTitle,
    scroll = "paper",
    fullScreen = false,
    dialogTitleSxProps,
    disableBackdropClick = false,
    disableEscapeKeyDown = false,
    disableCloseButton = false,
    sx,
    dialogTitleComponent,
    dialogActionsSxProps,
    dialogContentSxProps,

    hideBackdrop
}: ModaProps) => {
    const renderTitle_ = renderTitle !== undefined ? renderTitle : () => <>{title}</>;
    return (
        <Dialog
            component={dialogTitleComponent}
            hideBackdrop={hideBackdrop}
            open={open}
            onClose={(e, reason) => {
                if (reason === "backdropClick" && disableBackdropClick === true) {
                    return;
                }
                if (reason === "escapeKeyDown" && disableEscapeKeyDown === true) {
                    return;
                }

                onClose();
            }}
            maxWidth={maxWidth}
            scroll={scroll}
            fullWidth={fullWidth}
            fullScreen={fullScreen}
            PaperProps={{
                sx
            }}
        >
            <DialogTitle
                component={"div"}
                sx={{
                    fontSize: 30,
                    fontWeight: "bold",
                    pt: 1,
                    mb: 0,
                    ...dialogTitleSxProps,
                }}
            >
                {renderTitle_()}
            </DialogTitle>
            {!disableCloseButton && (
                <IconButton
                    aria-label="close"
                    onClick={onClose}
                    sx={{
                        position: 'absolute',
                        right: 8,
                        top: 8,
                        color: (theme) => theme.palette.grey[500],
                    }}
                >
                    <CloseIcon />
                </IconButton>
            )}

            <DialogContent
                sx={{
                    p: 3,
                    ...dialogContentSxProps
                }}
            >
                {content}
            </DialogContent>
            <DialogActions sx={{
                pb: 3,
                pr: 3,
                pl: 3,
                ...dialogActionsSxProps
            }}>
                {actions}
            </DialogActions>
        </Dialog>
    )
}

export default CustomModal