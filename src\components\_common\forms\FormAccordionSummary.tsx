import { TValues } from '@/interfaces/_common';
import { FormGroup } from '@/interfaces/forms';
import { useErrorsFormState } from '@/stores/formStore';
import ErrorIcon from '@mui/icons-material/Error';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AccordionSummary from '@mui/material/AccordionSummary';
import Typography from '@mui/material/Typography';

interface FormAccordionHeaderProps<TData extends TValues> {
    index: number;
    label: string;
    subLabel?: string;
    itemGroup: FormGroup<TData>
}
const FormAccordionHeader = <TData extends TValues>({ index, label, subLabel, itemGroup }: FormAccordionHeaderProps<TData>) => {
    const errors = useErrorsFormState();
    const hasError = errors.some((error) => error.groupId === itemGroup.id);
    return (
        <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            aria-controls={`panel${index}bh-content`}
            id={`panel${index}bh-header`}
        >
            <Typography sx={{ flexShrink: 0, fontWeight: "bold", marginRight: 2 }}>
                {label}
            </Typography>
            {subLabel && <Typography sx={{ color: 'text.secondary' }}>{subLabel}</Typography>}
            {hasError && <ErrorIcon color="error" />}
        </AccordionSummary>
    )
}

export default FormAccordionHeader