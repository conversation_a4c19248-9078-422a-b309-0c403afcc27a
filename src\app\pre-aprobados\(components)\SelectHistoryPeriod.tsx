import { EFilterControlsTypes, FilterControlState } from '@/interfaces/filterControls';
import { useFilterDataTableStore } from '@/stores/filterDataTableStore';
import Box from '@mui/material/Box';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';
import 'dayjs/locale/es';
import { useState } from 'react';

const SelectHistoryPeriod = () => {
    const [dateMonthView, setDateMonthView] = useState<Dayjs | null>(dayjs());
    const [dateYearView, setDateYearView] = useState<Dayjs | null>(dayjs());

    const setFilters = useFilterDataTableStore(state => state.setFilters);
    const addFilter = useFilterDataTableStore(state => state.addFilter);
    const filters = useFilterDataTableStore(state => state.filters);

    const handleChangeMonthView = (newValue: Dayjs | null) => {
        setDateMonthView(newValue);
        if (!newValue) return;
        const exist = filters.find((filter: FilterControlState) => filter.type == EFilterControlsTypes.DateMonth);

        if (exist) {
            setFilters(filters.map((filter: FilterControlState) => {
                if (filter.type == EFilterControlsTypes.DateMonth) {
                    return {
                        ...filter,
                        value: newValue.format("MM")
                    }
                }
                return filter;
            }));
        } else {
            addFilter({
                type: EFilterControlsTypes.DateMonth,
                value: newValue.format("MM"),
                key: 'dateMonth'
            });
        };
    };

    const handleChangeYearView = (newValue: Dayjs | null) => {
        setDateYearView(newValue);
        if (!newValue) return;

        const exist = filters.find((filter: FilterControlState) => filter.type == EFilterControlsTypes.DateYear);
        if (exist) {
            setFilters(filters.map((filter: FilterControlState) => {
                if (filter.type == EFilterControlsTypes.DateYear) {
                    return {
                        ...filter,
                        value: newValue.format("YYYY"),
                    }
                }
                return filter;
            }));
        } else {
            addFilter({
                type: EFilterControlsTypes.DateYear,
                value: newValue.format("YYYY"),
                key: 'dateYear'
            });
        };
    };

    return (
        <Box sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            width: "100%",
            gap: 2,
        }}>
            <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="es">
                <DatePicker
                    label={'Mes'}
                    views={['month']}
                    slotProps={{
                        textField: { sx: { width: 150 }, variant: "outlined", size: "small" },
                        calendarHeader: { sx: { display: "none" } },
                    }}
                    value={dateMonthView}
                    onChange={handleChangeMonthView}
                />
                <DatePicker
                    label={'Año'}
                    views={['year']}
                    slotProps={{
                        textField: { sx: { width: 100 }, variant: "outlined", size: "small" },
                    }}

                    value={dateYearView}
                    onChange={handleChangeYearView}
                />
            </LocalizationProvider>
        </Box>
    )
}

export default SelectHistoryPeriod