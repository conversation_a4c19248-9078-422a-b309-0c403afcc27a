import { util<PERSON>elper } from "@/helpers/utilHelper";
import { baseApi } from "@/lib/api";
import { ClientModel } from "@/models/responses/clientServices";
import { clientServices } from "@/services/clientServices";
import axios from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    client?: ClientModel,
    error?: string
    status: number
}
export async function GET(req: NextRequest, res: NextResponse) {
    let responseBody: Response = {
        client: undefined,
        error: undefined,
        status: 200
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 401;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    baseApi.defaults.nextRequest = req;

    const clientCode = utilHelper.getParamRequest(req.url, "clientCode");
    const docNumber = utilHelper.getParamRequest(req.url, "docNumber");

    try {
        if (clientCode) {
            const resp = await clientServices.getDataClientByCod(clientCode);
            responseBody.client = resp.data;
        } else if (docNumber) {
            const resp = await clientServices.getDataClientByDocNumber(docNumber);
            responseBody.client = resp.data;
        }
        return NextResponse.json(responseBody.client, { status: 200 });
    } catch (error: any) {
        console.error(error)
        responseBody.error = "Error al obtener los datos del cliente";
        responseBody.status = 500;
        if (axios.isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
        }
        return NextResponse.json(responseBody, { status: responseBody.status });
    }
}