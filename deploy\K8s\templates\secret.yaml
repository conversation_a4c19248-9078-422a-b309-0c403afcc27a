---
apiVersion: v1 # versión de la api de kubernetes que se está utilizando
kind: Secret # Un archivo secret gestiona variables con valores confidenciales encriptados en base64
metadata:
  name: {{ .Values.releaseName }}-secret # nombre del secret
  labels: # etiquetas para brindar amplitud en opciones de búsqueda
    app: {{ .Values.releaseName }}-app
    service: {{ .Values.releaseName }}
data: # los valores deben ir encriptados en base64 para ser aplicado en kubernetes.