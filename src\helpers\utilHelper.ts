import { clientServices } from "@/services/clientServices";
import dayjs from "dayjs";

const isClientSide = () => typeof window !== "undefined";

const getParamRequest = (url: string, name: string) => {
    const url_ = new URL(url ?? "");
    const searchParams = url_.searchParams;
    const value = searchParams.get(name);
    return value;
};

const validateParams = <T = string | any>(params: Array<T | null>) => {
    let isValid = true;
    for (let i = 0; i < params.length; i++) {
        const value = params[i];
        if (value === null || value === undefined) {
            isValid = false;
            break;
        }
    }
    return isValid;
}

const numberToCurrencyFormat = (numero: number | string) => {
    if(!utilHelper.esValido(numero)) return '';
    
    let numeroString = typeof numero === 'number' ? numero.toString() : numero;

    let partes = numeroString.split('.');
    let parteEntera = partes[0];
    let parteDecimal = partes.length > 1 ? partes[1] : '';

    parteEntera = parteEntera.replace(/\B(?=(\d{3})+(?!\d))/g, '.');

    let numeroFormateado = parteEntera + (parteDecimal.length ? ',' + parteDecimal : '');
    return numeroFormateado;
}

async function* obtenerEmpresas(codigos: string[]){
    for(const cod of codigos){
        const resp = await clientServices.getInternalDataClientByCode(cod);
        yield resp.data;
    }
}

const esValido = (valor: any) => {
    if (valor === null) return false;
    if (valor === undefined) return false;
    if (typeof valor === 'undefined') return false;
    if (valor.length === 0) return false;
    if (valor === '') return false;
    
    return true;
}

const obtenerTextoAntesDe = (text: string, separador: string) => {
    const atPosition = text.lastIndexOf(separador);
    if (atPosition === -1) {
        return text;
    }
    return text.slice(0, atPosition);
}

const calculatePeriodo = () => {
    const month_ = dayjs().add(-1, "month").format("MM");
    const year_ = dayjs().add(-1, "month").format("YYYY");
    return `${year_}${month_}`;
};

export const utilHelper = {
    isClientSide,
    getParamRequest,
    validateParams,
    numberToCurrencyFormat,
    obtenerEmpresas,
    esValido,
    obtenerTextoAntesDe,
    calculatePeriodo
}