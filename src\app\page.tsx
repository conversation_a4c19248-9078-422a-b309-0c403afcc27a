'use client'
import AutoAuth from './auth/signin/(components)/AutoAuth';
import { Alert, Box, Button, Divider, Typography } from '@mui/material';
import HistoryIcon from '@mui/icons-material/History';
import ListIcon from '@mui/icons-material/List';
import BuildCircleIcon from '@mui/icons-material/BuildCircle';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';

export default function Home() {
  AutoAuth({ urlRedireccion: '/' });

  return (
    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
      <Alert severity="info" icon={false} style={{
        marginTop: '12px', fontWeight: 'bold', fontSize: '17px',
        backgroundColor: '#F7F7F7', borderRadius: '12px', display: 'flex', alignItems: 'center', padding: '16px'
      }}>
        <Typography variant="h6" align='center' fontWeight='bold' marginBottom={4}>Módulo Pre Aprobados</Typography>
        <Divider variant="middle" sx={{ margin: 2, color: '#1D428A' }}>
          <Typography variant='caption'>MENU</Typography>
        </Divider>
        <Box style={{ display: 'flex', alignItems: 'center', gap: 8, flexDirection: 'column' }}>
          <Button fullWidth variant="contained" startIcon={<HistoryIcon />} href='/pre-aprobados/historial'>Histórico</Button>
          <Button fullWidth variant="contained" startIcon={<ListIcon/>} href='/pre-aprobados/listado'>Clientes Pre Aprobados</Button>
          <Button fullWidth variant="contained" startIcon={<BuildCircleIcon/>} href='/pre-aprobados/actualizarParametros'>Configuración de controles</Button>
          <Button fullWidth variant="contained" startIcon={<AdminPanelSettingsIcon/>} href='/pre-aprobados/accionesUsuario'>Configuración de Acciones del usuario</Button>
        </Box>
      </Alert>
    </div>
  )
}