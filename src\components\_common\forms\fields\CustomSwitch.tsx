"use client"
import { useInfoFieldPopper } from '@/hooks/useInfoFieldPopper';
import { useStatusFieldPopper } from '@/hooks/useStatusFieldPopper';
import { TValues } from '@/interfaces/_common';
import { PreApprovedParametersGroup } from '@/interfaces/parameters';
import { useFieldMetadataByFieldNameFormState, useFormStore } from '@/stores/formStore';
import { ErrorMessage } from '@hookform/error-message';
import FormLabel from '@mui/material/FormLabel';
import Switch, { SwitchProps } from '@mui/material/Switch';
import { useEffect } from 'react';
import { Controller, get, useFormContext } from 'react-hook-form';
import FormValidationMessage from '../FormValidationMessage';
import InfoFieldIcon from '../InfoFieldIcon';
import InfoFieldPopper from '../InfoFieldPopper';
import StatusFieldIcon from '../StatusFieldIcon';
import StatusFieldPopper from '../StatusFieldPopper';

interface CustomSwitchProps<TData extends TValues> extends Partial<Omit<SwitchProps, 'name'>> {
    name: keyof TData;
    label: string;
    info?: string;
    groupId: string;
}
const CustomSwitch = <TData extends TValues>({ name, label, info, groupId }: CustomSwitchProps<TData>) => {
    const { formState: { errors }, getValues } = useFormContext();

    const { handleClickInfo,
        anchorEl,
        open,
        id,
        handleClickAway
    } = useInfoFieldPopper(name as string);

    const {
        handleClickInfo: handleClickStatus,
        anchorEl: anchorElStatus,
        open: openStatus,
        id: idStatus,
        handleClickAway: handleClickAwayStatus
    } = useStatusFieldPopper(name as string + "status");

    const grupos = getValues("grupos") as PreApprovedParametersGroup[];
    const index = grupos.findIndex((grupo) => grupo.id === groupId);
    const name_ = `grupos.${index}.parameters.${name as string}`;
    const error_ = get(errors, name_);

    const addError = useFormStore((state) => state.addError);
    const removeErrors = useFormStore((state) => state.removeError);
    const fieldMetadata = useFieldMetadataByFieldNameFormState(name as string);
    const fieldMetadataStatus = fieldMetadata?.state ?? "approved";

    useEffect(() => {
        const key = name_ as string;
        if (error_) {
            const message = error_.message as string ?? "";
            addError({ groupId, field: key, message });
        } else {
            removeErrors({ groupId, field: key });
        }
    }, [errors]);



    return (
        <>
            <Controller
                name={name_}
                render={({ field }) => (<>
                    <FormLabel sx={{ fontWeight: "400", color: "black", display: "flex", alignItems: "center" }}>
                        {label}
                        {info && <InfoFieldIcon sx={{ marginLeft: "2px" }} onClick={handleClickInfo} />}
                        {fieldMetadata && <StatusFieldIcon sx={{ marginLeft: "2px" }} onClick={handleClickStatus} status={fieldMetadata.state} />}
                    </FormLabel>
                    <Switch
                        onChange={(e) => field.onChange(e.target.checked)}
                        checked={field.value}
                        disabled={fieldMetadataStatus == "pending"}
                    />
                </>
                )}
            />
            <ErrorMessage
                name={name_}
                errors={errors}
                render={({ message }) => <FormValidationMessage message={message} />}
            />
            {info && (
                <InfoFieldPopper
                    id={id}
                    open={open}
                    anchorEl={anchorEl}
                    info={info}
                    handleClickAway={handleClickAway}
                />
            )}

            {fieldMetadata && (
                <StatusFieldPopper
                    id={idStatus}
                    status={fieldMetadata.state}
                    open={openStatus}
                    anchorEl={anchorElStatus}
                    handleClickAway={handleClickAwayStatus}
                />
            )}
        </>
    )
}

export default CustomSwitch