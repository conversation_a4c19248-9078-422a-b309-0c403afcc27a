"use client"
import ConfirmationModal from '@/components/_common/Modal/ConfirmationModal'
import SubmitModal from '@/components/_common/Modal/SubmitModal'
import FormAccordion from '@/components/_common/forms/FormAccordion'
import { formHelper } from '@/helpers/formHelper'
import notify from '@/helpers/notifyHelper'
import { useFormParameters } from '@/hooks/parameters/useFormParameters'
import { useQueryParameters } from '@/hooks/parameters/useQueryParameters'
import { FormLayout } from '@/interfaces/forms'
import { PreApprovedFormParameters, PreApprovedParameters } from '@/interfaces/parameters'
import { useFormStore } from '@/stores/formStore'
import { CircularProgress, Typography } from '@mui/material'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Grid from '@mui/material/Unstable_Grid2/Grid2'
import { useEffect, useMemo, useState } from 'react'
import { FormProvider } from 'react-hook-form'
import { checkPermisosAcciones } from '../(config)/checkPermisosAcciones'
import { ROLES_PARAMETROS } from '../(enums)/RolesParametrosEnum'

interface FormProps {
    groupId: string;
    formParametersLayout: FormLayout<PreApprovedParameters>
}
export default function Form({ groupId, formParametersLayout }: FormProps) {
    const [accionesHabilitadas, setAccionesHabilitadas] = useState<{ [key: string]: boolean }>({});
    const {
        data,
        isLoading,
        error,
        isRefetching,
    } = useQueryParameters(groupId);

    const {
        methods,
        isSubmiting,
        showConfirmModal,
        showSubmitModal,
        handleCloseSubmitModal,
        handleCloseConfirmModal,
        handleConfirmModal,
        handleCancelModal,
        submitError,
        submitText,
        setShowConfirmModal,
    } = useFormParameters({ groupId, defaultValues: data?.data });

    const formLayout = useMemo(() => {
        return formParametersLayout;
    }, []);

    useEffect(() => {
        const obtenerPermisos = async () => {
            const permisos = await checkPermisosAcciones([ROLES_PARAMETROS.MODIFICAR_REGLAS]);
            setAccionesHabilitadas(permisos);
        };

        obtenerPermisos();
    }, []);

    const loading = isLoading || isRefetching;

    const eventoRecargar = () => {
        window.location.reload();
    };

    return (
        <Grid
            xs={8}
        >
            {(loading) && (
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    <CircularProgress />
                </Box>
            )}
            {(error || data?.error) && <Typography>{error?.message ?? ""} {data?.error ?? ""}</Typography>}

            {
                !loading && data?.error === null && error === null && (
                    <FormProvider {...methods}>
                        <Box
                            component="form"
                            onSubmit={async (e) => {
                                e.preventDefault();
                                const validation = await methods.trigger();

                                if (validation) {
                                    const pendingsFields = useFormStore.getState().fieldsMetadata.filter(f => f.state == "pending")
                                    if (pendingsFields.length > 0) {
                                        notify.error("Hay parámetros pendientes de autorización");
                                        return;
                                    }

                                    const values = methods.getValues();
                                    const dirtyFields = methods.formState.dirtyFields;
                                    const defaultValues = methods.formState.defaultValues as PreApprovedFormParameters;
                                    const modifiedFields = formHelper.getItemsDirtyData(values, dirtyFields, defaultValues);
                                    let excEmpresasOriginal = defaultValues.grupos[0]?.parameters.exclusionEmpresas ?? [];
                                    let excSucursalesModificado = values.grupos[0]?.parameters?.exclusionSucursales ?? [];
                                    let excEmpresasModificado = values.grupos[0]?.parameters?.exclusionEmpresas ?? [];

                                    let modSucursales = excSucursalesModificado.some(modificado => modificado.hasOwnProperty('isNew') || modificado.hasOwnProperty('isDeleted'));
                                    let modEmpresas = !(excEmpresasOriginal.length === excEmpresasModificado.length && excEmpresasOriginal.every((element, index) => element === excEmpresasModificado[index]));

                                    if ((!modifiedFields.grupos || (modifiedFields.grupos && modifiedFields.grupos.length == 0)) && !modSucursales && !modEmpresas) {
                                        notify.error("No hay cambios para guardar");
                                        return;
                                    }
                                    setShowConfirmModal(true);
                                } else {
                                    notify.warning("Existen parámetros pendientes de autorización");
                                }
                            }}
                            noValidate
                        >
                            <FormAccordion formLayout={formLayout} />
                            <Box sx={{
                                display: 'flex',
                                justifyContent: 'flex-end',
                                gap: 2,
                                marginTop: 2
                            }}>
                                <Button
                                    variant="contained"
                                    color="inherit"
                                    size="large"
                                    onClick={eventoRecargar}
                                >
                                    Cancelar
                                </Button>
                                <Button
                                    variant="contained"
                                    type="submit"
                                    size="large"
                                    disabled={!accionesHabilitadas.modificarreglas}
                                >
                                    Guardar
                                </Button>
                            </Box>
                        </Box>
                    </FormProvider>
                )
            }

            <ConfirmationModal
                open={showConfirmModal}
                onClose={handleCloseConfirmModal}
                confirmationText="¿Está seguro que desea guardar los cambios?"
                onConfirm={handleConfirmModal}
                onCancel={handleCancelModal}
                confirmText='Sí, estoy seguro'
                cancelText='No, cancelar'
            />
            <SubmitModal
                open={showSubmitModal}
                onClose={handleCloseSubmitModal}
                text={submitText}
                onClickActionButton={handleCloseSubmitModal}
                error={submitError}
                isLoading={isSubmiting}
            />
        </Grid>
    )
}