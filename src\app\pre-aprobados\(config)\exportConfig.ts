import { HeaderExportMapper } from "@/interfaces/_common";
import { PreApprovedClientsModel, TCClientsModel } from "@/models/responses/clientServices";

export const exportConfigPreApprovedClients: HeaderExportMapper<PreApprovedClientsModel>[] = [
    { header: "Codigo Cliente", accessorFn: (row: PreApprovedClientsModel) => row.codigoCliente },
    { header: "Codigo Cuenta", accessorFn: (row: PreApprovedClientsModel) => row.cuenta },
    { header: "Monto Prestamo Maximo", accessorFn: (row: PreApprovedClientsModel) => row.montoPrestamo },
    { header: "Edad", accessorFn: (row: PreApprovedClientsModel) => row.edad },
    { header: "Campanha", accessorFn: (row: PreApprovedClientsModel) => row.campanha },
    { header: "Antiguedad", accessorFn: (row: PreApprovedClientsModel) => row.antiguedad },
    { header: "Calificacion Contigo", accessorFn: (row: PreApprovedClientsModel) => row.calificacionConti },
    { header: "Calificacion BCP", accessorFn: (row: PreApprovedClientsModel) => row.calificacionBCP },
    { header: "Ref. Atraso", accessorFn: (row: PreApprovedClientsModel) => row.referenciaAtraso },
    { header: "Es Codeudor", accessorFn: (row: PreApprovedClientsModel) => row.codeudor },
    { header: "Mora", accessorFn: (row: PreApprovedClientsModel) => row.mora },
    { header: "Sucursal", accessorFn: (row: PreApprovedClientsModel) => row.sucursal },
    { header: "Lista Negativas", accessorFn: (row: PreApprovedClientsModel) => row.ald },
    { header: "ROS", accessorFn: (row: PreApprovedClientsModel) => row.ros },
    { header: "Cant. Prestamos", accessorFn: (row: PreApprovedClientsModel) => row.cantidadPrestamo },
];

export const exportConfigTcClients: HeaderExportMapper<TCClientsModel>[] = [
    { header: "Tipo Cuenta", accessorFn: (row) => row.tipoCuenta },
    { header: "Id Afinidad", accessorFn: (row) => row.idAfinidad },
    { header: "Departamento", accessorFn: (row) => row.departamento },
    { header: "Ciudad", accessorFn: (row) => row.ciudad },
    { header: "Oficial", accessorFn: (row) => row.oficial },
    { header: "Cuenta", accessorFn: (row) => row.cuenta },
    { header: "Cuenta Vip", accessorFn: (row) => row.cuentaVip },
    { header: "Cuenta Franqueo", accessorFn: (row) => row.cuentaFranqueo },
    { header: "Seguro Vida", accessorFn: (row) => row.seguroVida },
    { header: "Cobro Cargos", accessorFn: (row) => row.cobroCargos },
    { header: "Tipo Costo", accessorFn: (row) => row.tipoCosto },
    { header: "Retener Extracto", accessorFn: (row) => row.retenerExtracto },
    { header: "Codigo Cliente", accessorFn: (row) => row.codigoCliente },
    { header: "Linea", accessorFn: (row) => row.linea },
    { header: "Linea Credito", accessorFn: (row) => row.lineaCredito },
    { header: "Cuota", accessorFn: (row) => row.cuota },
    { header: "Tipo Pago", accessorFn: (row) => row.tipoPago },
    { header: "Valor CFG", accessorFn: (row) => row.valorCFG },
    { header: "Tasa Financiacion", accessorFn: (row) => row.tasaFinanciacion },
    { header: "Factor Vip", accessorFn: (row) => row.factorVip },
    { header: "Motivo Retencion", accessorFn: (row) => row.motivoRetencion },
];