import { TValues } from '@/interfaces/_common'
import Box from '@mui/material/Box'
import { MRT_TableInstance } from 'material-react-table'
import React from 'react'

interface CustomBottomFooterProps<TData extends TValues> {
    table: MRT_TableInstance<TData>
}
const CustomBottomFooter = <TData extends TValues>({ table }: CustomBottomFooterProps<TData>) => {
    return (
        <Box>
            
        </Box>
    )
}

export default CustomBottomFooter