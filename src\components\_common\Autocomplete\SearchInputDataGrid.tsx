import { ESearchServices } from '@/constants/utils';
import notify from '@/helpers/notifyHelper';
import { OptionListGridItem } from '@/interfaces/_common';
import { searchServices } from '@/services/searchServices';
import SearchIcon from '@mui/icons-material/Search';
import { CircularProgress, IconButton, InputAdornment, TextField } from '@mui/material';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { SxProps, Theme } from '@mui/material/styles';
import React, { useState } from 'react';
import SimpleSelect from '../SimpleSelect';

interface SearchInputDataGridProps {
    label: string;
    value?: string;
    sx?: SxProps<Theme>;
    labelPropsSx?: SxProps<Theme>;
    valuePropsSx?: SxProps<Theme>;
    searchService: ESearchServices;
    onComplete?: (data: OptionListGridItem | null) => void;
    disabled?: boolean;
}
const SearchInputDataGrid = ({
    label, value, sx, labelPropsSx, valuePropsSx, searchService, onComplete, disabled
}: SearchInputDataGridProps) => {
    const [value_, setValue_] = useState(value ?? "");
    const [valueSelect, setValueSelect] = useState("0");
    const [isLoading, setLoading] = useState(false);
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setValue_(e.target.value);
    }

    const handleChangeSelect = (value: string) => {
        setValueSelect(value);
    }

    const handleClickSearch = async () => {
        if (value_?.trim() == "") return;
        setLoading(true);
        switch (searchService) {
            case ESearchServices.COMPANY:
                const data = valueSelect == "0" ?
                    await searchServices.companyByCode(value_ ?? "")
                    : await searchServices.companyByRuc(value_ ?? "");


                if (data?.data) {
                    onComplete && onComplete(data?.items as OptionListGridItem);
                } else {
                    notify.error("No se encontró la empresa");
                }
                break;
            default:
                break;
        }
        setLoading(false);
    }
    const currentValue = value_ ?? "";
    return (
        <Box sx={{
            display: "flex",
            gap: 1,
            alignItems: "center",
            marginBottom: "10px",
            ...sx
        }}>
            <Typography
                fontWeight={"bold"}
                sx={{ ...labelPropsSx }}
            >
                {label}:
            </Typography>
            <TextField
                variant="standard"
                value={currentValue}
                fullWidth
                disabled={isLoading}
                sx={{
                    backgroundColor: "#e0e0e0",
                    borderRadius: "5px",
                    ...valuePropsSx
                }}
                InputProps={{
                    endAdornment: isLoading ?
                        <InputAdornment position="start">
                            <CircularProgress size={22} />
                        </InputAdornment>
                        : null,
                }}
                inputProps={{
                    sx: {
                        padding: "4px 5px 5px",
                        width: "100%",
                        overflow: "auto",
                        textOverflow: "ellipsis",
                    },
                }}
                onChange={handleChange}
            />
            <SimpleSelect label=''
                options={[
                    {
                        label: "Cod. Empresa",
                        value: "0"
                    },
                    {
                        label: "RUC",
                        value: "1"
                    },
                ]}
                value={valueSelect}
                onChange={handleChangeSelect}
            />
            <IconButton
                size="small"
                color='primary'
                sx={{
                    padding: "5px",
                    margin: "0px",
                    backgroundColor: "#e0e0e0",
                }}
                disabled={isLoading || disabled}
                onClick={handleClickSearch}
            >
                <SearchIcon />
            </IconButton>
        </Box>
    )
}

export default SearchInputDataGrid