import React, { useState } from 'react';
import {
    Modal,
    Box,
    Typography,
    Select,
    Chip,
    MenuItem,
    Button,
    OutlinedInput,
    InputLabel,
    SelectChangeEvent
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import { RolesParametros } from '@/models/responses/usuariosServices';

interface ModalGestionAccionesProps {
    open: boolean;
    handleClose: () => void;
    handleGuardarCambios: () => void;
    rolesParametros: RolesParametros[];
    selectedRoles: string[];
    handleChangeSelectedRoles: (event: SelectChangeEvent<string | string[]>) => void;
}

const ModalGestionAcciones: React.FC<ModalGestionAccionesProps> = ({
    open,
    handleClose,
    handleGuardarCambios,
    rolesParametros,
    selectedRoles,
    handleChangeSelectedRoles
}) => {
    const [guardandoCambios, setGuardandoCambios] = useState(false);

    const handleGuardarClick = async () => {
        setGuardandoCambios(true);
        await handleGuardarCambios();
        setGuardandoCambios(false);
    };

    return (
        <Modal
            open={open}
            onClose={handleClose}
            aria-labelledby="modal-modal-title"
            aria-describedby="modal-modal-description"
        >
            <Box sx={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', width: 400, bgcolor: 'background.paper', boxShadow: 24, p: 3 }}>
                <Typography id="modal-modal-title" variant="h4" component="h4">
                    Gestionar Acciones
                </Typography>
                <Box id="modal-modal-description" sx={{ mt: 2, mb: 4 }}>
                    <InputLabel id="roles-seleccionados-label">Seleccionar Acciones</InputLabel>
                    <Select
                        labelId="roles-seleccionados-label"
                        id="roles-seleccionados"
                        multiple
                        value={selectedRoles}
                        style={{ width: '100%' }}
                        input={<OutlinedInput id="select-multiple-chip" label="Seleccionar Roles" />}
                        onChange={handleChangeSelectedRoles}
                        renderValue={(selected) => (
                            <Box sx={{ display: 'flex', flexWrap: 'wrap' }}>
                                {Array.isArray(selected) ? selected.map((value) => (
                                    <Chip key={value} label={value} style={{ margin: 2 }} />
                                )) : <Chip key={selected} label={selected} style={{ margin: 2 }} />}
                            </Box>
                        )}
                    >
                        {rolesParametros.map((rol) => (
                            <MenuItem key={rol.idRol} value={rol.descripcion}>
                                {rol.descripcion}
                            </MenuItem>
                        ))}
                    </Select>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'end', marginTop: '16px' }}>
                    <Button
                        variant="contained"
                        color="error"
                        startIcon={<CancelIcon />}
                        onClick={handleClose}
                        style={{ marginRight: '4px' }}
                        size="medium"
                    >
                        Cancelar
                    </Button>

                    <Button
                        variant="contained"
                        color="primary"
                        startIcon={<SaveIcon />}
                        onClick={handleGuardarClick}
                        size="medium"
                        style={{ fontSize: '13px' }}
                        disabled={guardandoCambios}
                    >
                        { guardandoCambios ? 'Guardando...' : 'Guardar Cambios' }
                    </Button>
                </Box>
            </Box>
        </Modal>
    );
};

export default ModalGestionAcciones;