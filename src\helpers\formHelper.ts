import { PreApprovedParameters, PreApprovedParametersGroup } from '@/interfaces/parameters';
import { configParametersMapping } from '@/mapper/mappingParametersData';
type ArrayItem_1 = Record<string, unknown> | string | number;

export const getItemsDirtyData = <
    TData extends Record<keyof TDirtyItems, unknown>,
    TDirtyItems extends Record<string, unknown>,
>(
    data: TData,
    dirtyItems: TDirtyItems,
    defaultValues: TData,
    isArray?: boolean
): Partial<TData> => {
    const dirtyItemsEntries = Object.entries(dirtyItems);
    return dirtyItemsEntries.reduce((dirtyData, [name, value]) => {
        if (typeof value !== "object") {
            let modified = false;
            if (Array.isArray(data[name]) && Array.isArray(defaultValues[name])) {
                const data_ = data[name] as unknown as Array<ArrayItem_1>;
                const defaultData_ = defaultValues[name] as unknown as Array<ArrayItem_1>;
                const everyValid = data_.every((item_, index) => {
                    let modified_ = false;
                    const defaultItem_ = defaultData_.find((item) => {
                        return item == item_;
                    });
                    if (typeof defaultItem_ === "undefined") {
                        modified_ = true;
                    } else if (typeof defaultItem_ == "string" || typeof defaultItem_ == "number") {
                        if (item_ != defaultItem_) {
                            modified_ = true;
                        }
                    } else {
                        const itemEntries = Object.entries(item_);
                        modified_ = itemEntries.every(([key, value]) => {
                            if (value !== defaultItem_[key]) {
                                modified_ = true;
                            }
                        });
                    }
                    if (modified_) {
                        return false;
                    }

                    return true;
                });
                modified = !everyValid;
            } else if (data[name] != defaultValues[name]) {
                modified = true;
            }
            const modifiedValue = modified ? {
                ...dirtyData,
                [name]: data[name],
            } : { ...dirtyData };
            return modifiedValue
        }

        const valueIsArray = Array.isArray(value);
        const nestedItemsDirtyData = getItemsDirtyData(
            data[name] as TData,
            dirtyItems[name] as TDirtyItems,
            defaultValues[name] as TData,
            valueIsArray
        );
        //@ts-ignore
        const itemDirtyData_ = Object.keys(dirtyData).length > 0 ? dirtyData[name] : [];
        if (valueIsArray && Array.isArray(itemDirtyData_)) {
            const keys_ = Object.keys(nestedItemsDirtyData);
            keys_.forEach((key_) => {
                itemDirtyData_[parseInt(key_)] = nestedItemsDirtyData[key_];
            });
        }
        const itemDirtyData = valueIsArray ?
            [...itemDirtyData_]
            : nestedItemsDirtyData;

        let extraData = {};
        if (name == "parameters") {
            extraData = {
                id: data.id,
            }
        }
        return {
            ...dirtyData,
            ...extraData,
            [name]: itemDirtyData,
        }
    }, {});
}

const addRequiredFields = (
    {
        values,
        modifiedFields,
        defaultValues
    }: {
        values: PreApprovedParametersGroup[],
        modifiedFields: PreApprovedParametersGroup[],
        defaultValues: PreApprovedParametersGroup[],
    }
) => {
    const requiredFields: string[] = [
        "conceptosAceptadosParaPromedio",
        "exclusionEmpresas",
        "exclusionSucursales",
    ];
    return values.map((value, index) => {
        const keyParameters = Object.keys(value.parameters);
        const groupId = value.id;

        keyParameters.forEach((key) => {
            if (requiredFields.includes(key)) {
                const existGroup = modifiedFields.find((item) => item.id == groupId);
                const config = configParametersMapping.find((item) => item.name == key);

                const parameterItem = value.parameters;
                if (existGroup) {
                    if (config && config.isModified && config.isModified(parameterItem, defaultValues[index].parameters)) {
                        existGroup.parameters = {
                            ...existGroup.parameters,
                            [key]: parameterItem[key as keyof PreApprovedParameters]
                        };
                    }
                } else {
                    if (config && config.isModified && config.isModified(parameterItem, defaultValues[index].parameters)) {
                        modifiedFields.push({
                            id: groupId,
                            parameters: {
                                [key]: parameterItem[key as keyof PreApprovedParameters]
                            }
                        });
                    }
                }
            }
        });

        return modifiedFields;
    });

}

export const formHelper = {
    getItemsDirtyData,
    addRequiredFields
}