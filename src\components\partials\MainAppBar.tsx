"use client"
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Grid from '@mui/material/Unstable_Grid2';
import Logo from "@/assets/images/logo-continental-blanco.svg";
import notify from '@/helpers/notifyHelper';
import { authServices } from '@/services/authServices';
import LogoutIcon from '@mui/icons-material/Logout';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { signOut, useSession } from 'next-auth/react';
import Image from 'next/image';

const MainAppBar = () => {
    const { status, data } = useSession();

    const fullNameUser = status == "authenticated" ? data?.user?.name ?? "" : "";
    const handleSignOut = async () => {
        try {
            await authServices.internalLogout();
            signOut();
        } catch (error) {
            notify.error("Ocurrió un error al cerrar sesión");
        }
    }

    return (
        <AppBar style={{ background: '#1D428A' }} sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }} >
            <Toolbar disableGutters style={{ marginRight: 70, marginLeft: 70 }}>
                <Grid container >
                    <Grid sx={{ cursor: 'pointer' }}>
                        <Image src={Logo} alt={'Logo'} priority={true} />
                    </Grid>
                </Grid>
                <Box sx={{ flexGrow: 0, ml: "auto", display: "flex" }}>
                    <Typography style={{ paddingRight: 8 }}>
                        {fullNameUser}
                    </Typography>
                    <Tooltip title="Cerrar sesión">
                        <IconButton sx={{ p: 0 }} onClick={handleSignOut}>
                            <LogoutIcon sx={{ display: { md: 'flex' }, mr: 1, color: 'white' }} />
                        </IconButton>
                    </Tooltip>
                </Box>
            </Toolbar>
        </AppBar>
    )
}

export default MainAppBar