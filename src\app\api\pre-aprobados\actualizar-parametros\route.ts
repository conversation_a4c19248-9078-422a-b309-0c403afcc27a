import { preAprobadosApi } from "@/lib/api";
import { ParametersModel } from "@/models/responses/parameterService";
import { preApprovedService } from "@/services/preApprovedService";
import axios from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    updated: boolean
    error?: string
    status: number
    meta?: any
}
export async function POST(req: NextRequest) {
    let responseBody: Response = {
        updated: false,
        error: "Error al procesar la solicitud de actualización de parametros",
        status: 200,
        meta: undefined
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 401;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    preAprobadosApi.defaults.nextRequest = req;

    const body = await req.json() as { userCode: string, parameters: ParametersModel[] };

    const userCode = body.userCode;
    const parameters = body.parameters;

    try {
        const resp = await preApprovedService.updateParameter(parameters, userCode);
        responseBody.updated = resp.data;
        return NextResponse.json(responseBody.updated, { status: 200 });
    } catch (error: any) {
        responseBody.error = "Error al actualizar los parametros";
        responseBody.status = 500;
        if (axios.isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
            responseBody.meta = {
                data: error.config?.data,
                url: error.config?.url,
            };
        }
        console.error(error);
        return NextResponse.json(responseBody, { status: responseBody.status });
    }


}