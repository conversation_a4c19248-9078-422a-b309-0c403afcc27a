import { create } from "zustand";

export type FormGroupTabs = string;
export interface FormGroupTabState {
    activeTab: FormGroupTabs | null;
    setActiveTab: (tabValue: string) => void;
};

export const useFormGroupTabStore = create<FormGroupTabState>((set) => ({
    activeTab: "1",
    setActiveTab: (tabValue: string) => set(
        (state) => ({ activeTab: tabValue })
    ),
}));

export const useActiveTab = () => {
    const activeTab = useFormGroupTabStore((state) => state.activeTab);
    return activeTab;
}