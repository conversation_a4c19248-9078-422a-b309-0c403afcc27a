import { util<PERSON>elper } from "@/helpers/utilHelper";
import { baseApi } from "@/lib/api";
import { usuarioServices } from "@/services/usuarioServices";
import axios from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    data?: {},
    error?: string
    status: number
    meta?: any
}
export async function GET(req: NextRequest) {
    let responseBody: Response = {
        data: undefined,
        error: undefined,
        status: 200
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 401;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    baseApi.defaults.nextRequest = req;

    const searchParams = req.nextUrl.searchParams
    const usuarioDominio = searchParams.get('usuarioDominio');

    try {
        if (utilHelper.esValido(usuarioDominio)) {
            const resp = await usuarioServices.getUsuarioPorDominio(usuarioDominio!);
            responseBody.data = resp.data;
            return NextResponse.json(responseBody.data, { status: 200 });
        } else {
            responseBody.error = "El usuario no es válido";
            responseBody.status = 400;
            return NextResponse.json(responseBody, { status: responseBody.status });
        }
    } catch (error: any) {
        console.error(error);
        responseBody.error = "Error al obtener los datos del cliente";
        responseBody.status = 500;
        if (axios.isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
            responseBody.meta = {
                data: error.config?.data,
                url: error.config?.url,
                test: process.env.NEXT_PUBLIC_BASE_API_URL
            };
        }
        return NextResponse.json(responseBody, { status: responseBody.status });
    }
}