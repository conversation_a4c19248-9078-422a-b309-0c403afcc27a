"use client"
import Box from '@mui/material/Box'
import Button from '@mui/material/Button';
import { useRouter } from 'next/navigation';
import React from 'react'

const PageActions = () => {
    const router = useRouter();
    const handleClickNew = () => {
        router.push('/parametros/new');
    }
    return (
        <Box>
            <Button onClick={handleClickNew}>Crear</Button>
        </Box>
    )
}

export default PageActions