import { baseApiInterceptor, internalApiErrorResponseInterceptor, internalApiInterceptor } from "@/config/apiInterceptor";
import axios from "axios";
import { NextRequest } from "next/server";

declare module 'axios' {
	export interface AxiosRequestConfig {
		nextRequest?: NextRequest;
	}
}

const baseApiUrl = process.env.NEXT_PUBLIC_BASE_API_URL;
const baseApi = axios.create({ baseURL: baseApiUrl });
const preAprobadosApi = axios.create({ baseURL: baseApiUrl });

baseApi.defaults.headers.common = {
	'Content-Type': 'application/json',
	'Grant-Type': 'client_credentials',
	'Scope': 'profile',
	'Canal': 'web'
};

preAprobadosApi.defaults.headers.common = {
	'Content-Type': 'application/json',
	'Grant-Type': 'client_credentials',
	'Scope': 'profile'
};

baseApi.interceptors.request.use(baseApiInterceptor);
baseApi.interceptors.response.use((response) => response,
	(error) => {
		return Promise.reject(error);
	}
);
preAprobadosApi.interceptors.request.use(baseApiInterceptor);
preAprobadosApi.interceptors.response.use((response) => response,
	(error) => {
		return Promise.reject(error);
	}
);

const baseAppUrl = "/api";
const internalApi = axios.create({ baseURL: baseAppUrl });

internalApi.interceptors.request.use(internalApiInterceptor);
internalApi.interceptors.response.use((response) => response, internalApiErrorResponseInterceptor)

export {
    baseApi,
    preAprobadosApi,
	internalApi
};