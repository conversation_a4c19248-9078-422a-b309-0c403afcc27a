import { externalEndpoints, internalEndpoints } from "@/config/endpoints";
import { baseApi, internalApi } from "@/lib/api";
import { SucursalModel } from "@/models/responses/authServices";

const getAllBranches = async () => {
    return await baseApi.get<SucursalModel[]>(externalEndpoints.comunes.obtenerSucursales);
}

const getInternalAllBranches = async () => {
    return await internalApi.get<SucursalModel[]>(internalEndpoints.datos.obtenerSucursales);
}

export const commonServices = {
    getAllBranches,
    getInternalAllBranches
}