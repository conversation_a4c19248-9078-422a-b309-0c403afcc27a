"use client"
import { schemaFormParameters } from '@/app/pre-aprobados/yupSchema';
import { PreApprovedFormParameters } from '@/interfaces/parameters';
import { mappingEntryParameters, mappingOutputParameters } from '@/mapper/mappingParametersData';
import { ParametersModel } from '@/models/responses/parameterService';
import { preApprovedService } from '@/services/preApprovedService';
import { yupResolver } from '@hookform/resolvers/yup';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import useCustomForm from '../useCustomForm';
import { formHelper } from '@/helpers/formHelper';
import { useAuthStore } from '@/stores/authStore';

interface FormParameters {
    groupId: string;
    defaultValues?: ParametersModel[] | null;
}
const useFormParameters = ({ groupId, defaultValues }: FormParameters) => {
    const [isSubmiting, setIsSubmiting] = useState(false);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [showSubmitModal, setShowSubmitModal] = useState(false);
    const [submitText, setSubmitText] = useState('');
    const [submitError, setSubmitError] = useState(false);
    const queryClient = useQueryClient();

    const methods = useCustomForm({
        defaultValues: defaultValues ? mappingEntryParameters(defaultValues) : {} as PreApprovedFormParameters,
        resolver: yupResolver(schemaFormParameters)
    });

    const handleSubmit = async (values: PreApprovedFormParameters) => {
        setIsSubmiting(true);
        setSubmitError(false);
        
        try {
            const userCode = useAuthStore.getState().user?.codigo ?? '';
            let valoresFormulario = defaultValues ?? [] as ParametersModel[];
            const mapped = mappingOutputParameters(values, groupId, valoresFormulario)
            if (mapped.length > 0) {
                const resp = await preApprovedService.updateInternalParameters(mapped, userCode);
                if (resp.status === 200) {
                    setIsSubmiting(false);
                    setShowSubmitModal(true);
                    setSubmitText('Tu solicitud fue enviada al autorizador');
                    methods.reset();
                    await queryClient.fetchQuery({
                        queryKey: ['queryParameters', groupId],
                    });
                }
            } else {
                setIsSubmiting(false);
                setShowSubmitModal(false);
            }
        } catch (error) {
            setIsSubmiting(false);
            setSubmitError(true);
            setSubmitText('Error al guardar los datos');
            console.error(error);
        }
    }

    const handleCloseConfirmModal = () => {
        setShowConfirmModal(false);
    }

    const handleCloseSubmitModal = () => {
        setShowSubmitModal(false);
    }

    const handleConfirmModal = () => {
        setShowSubmitModal(true);
        setShowConfirmModal(false);

        methods.handleSubmit(() => {
            const values = methods.getValues();
            const dirtyFields = methods.formState.dirtyFields ?? {};
            const defaultValues = methods.formState.defaultValues as PreApprovedFormParameters;

            const modifiedFields = formHelper.getItemsDirtyData(values, dirtyFields, defaultValues) ?? {};

            let requiredFields = formHelper.addRequiredFields({
                values: values.grupos,
                modifiedFields: modifiedFields.grupos ?? [],
                defaultValues: defaultValues.grupos,
            });

            if (modifiedFields?.grupos?.length === 0 || requiredFields == null) {
                setShowSubmitModal(false);
                return;
            }
            const grupos = modifiedFields?.grupos ?? (requiredFields[0] ?? []);

            handleSubmit({ grupos });
        })();
    }

    const handleCancelModal = () => {
        setShowConfirmModal(false);
    }

    const handleSuccessModal = () => {
        setShowSubmitModal(false);
    }

    useEffect(() => {
        if (defaultValues) {
            methods.reset(mappingEntryParameters(defaultValues));
        }
    }, [defaultValues]);

    return {
        methods,
        handleSubmit,
        isSubmiting,
        showConfirmModal,
        setShowConfirmModal,
        showSubmitModal,
        handleConfirmModal,
        handleCloseConfirmModal,
        handleCloseSubmitModal,
        handleCancelModal,
        handleSuccessModal,
        submitText,
        setSubmitText,
        submitError,
    };
}
export {
    useFormParameters
};