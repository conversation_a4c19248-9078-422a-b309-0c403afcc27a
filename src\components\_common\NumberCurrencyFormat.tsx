import { CURRENCY } from '@/constants/_common';
import { InputBaseComponentProps } from '@mui/material/InputBase';
import React from 'react';
import { NumberFormatValues, NumericFormat, NumericFormatProps, SourceInfo } from "react-number-format";

type BaseNumberCurrencyFormatProps = Partial<InputBaseComponentProps> & Omit<NumericFormatProps, 'onValueChange'>;
interface NumberCurrencyFormatProps extends BaseNumberCurrencyFormatProps{
    name: string;
    onChange: (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}
export const NumberCurrencyFormat = React.forwardRef<HTMLInputElement, NumberCurrencyFormatProps>(
    function NumberFormatCustom(props, ref) {
        const { onChange, ...other } = props;

        return (
            <NumericFormat
                {...other}
                getInputRef={ref}
                onValueChange={(values: NumberFormatValues, sourceInfo: SourceInfo) => {
                    if (onChange) {
                        onChange({
                            target: {
                                name: props.name,
                                value: values.value,
                            },
                        } as React.ChangeEvent<HTMLInputElement>);
                    }
                }}
                thousandSeparator={CURRENCY.thousandSeparator}
                decimalScale={CURRENCY.decimalScale}
                decimalSeparator={CURRENCY.decimalSeparator}
                valueIsNumericString
            />
        );
    }
);