import { useInfoFieldPopper } from '@/hooks/useInfoFieldPopper';
import { OptionItemCreatable, TValues } from '@/interfaces/_common';
import { PreApprovedParametersGroup } from '@/interfaces/parameters';
import { ErrorMessage } from '@hookform/error-message';
import AddIcon from '@mui/icons-material/Add';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import DoneIcon from '@mui/icons-material/Done';
import RemoveIcon from '@mui/icons-material/Remove';
import { Box, SxProps, Theme, styled } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import Chip from '@mui/material/Chip';
import IconButton from '@mui/material/IconButton';
import TextField from '@mui/material/TextField';
import { Controller, useFormContext } from 'react-hook-form';
import FormValidationMessage from '../FormValidationMessage';
import InfoFieldPopper from '../InfoFieldPopper';

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;
const RemoveIconButtonStyled = styled(IconButton)(({ theme }) => ({
    color: "white",
    backgroundColor: theme.palette.error.main,
    '&:hover': {
        backgroundColor: theme.palette.error.dark,
    },
}));
const AddIconButtonStyled = styled(IconButton)(({ theme }) => ({
    color: "white",
    backgroundColor: "#1D428A",
    '&:hover': {
        backgroundColor: theme.palette.primary.dark,
    },
}));

interface CustomMultiSelectAdd<TData extends TValues> {
    id: string;
    name: keyof TData;
    label: string;
    groupId: string;
    placeholder?: string;
    shouldUnregister?: boolean;
    required?: boolean;
    sx?: SxProps<Theme>;
    info?: string;
}
const CustomMultiSelectAdd = <TData extends TValues>(props: CustomMultiSelectAdd<TData>) => {
    const {
        id,
        name,
        label,
        placeholder,
        shouldUnregister,
        info,
        groupId,
        sx
    } = props;
    const { formState: { errors }, getValues } = useFormContext();

    const grupos = getValues("grupos") as PreApprovedParametersGroup[];
    const index = grupos.findIndex((grupo) => grupo.id === groupId);
    const name_ = `grupos.${index}.parameters.${name as string}`;

    const { handleClickInfo,
        anchorEl,
        open,
        id: idPopper,
        handleClickAway
    } = useInfoFieldPopper(name_);

    return (
        <>
            <Controller
                name={name_}
                shouldUnregister={shouldUnregister}
                render={({ field }) => {
                    return (
                        <Autocomplete<OptionItemCreatable, true, false, true>
                            id={id}
                            sx={sx}
                            multiple
                            options={field.value ?? []}
                            disableCloseOnSelect
                            handleHomeEndKeys
                            selectOnFocus
                            freeSolo
                            getOptionLabel={(option) => {
                                if (typeof option === 'string') {
                                    return option;
                                }
                                if (option) {
                                    return option.inputValue;
                                }
                                return (option as OptionItemCreatable).value;
                            }}
                            renderOption={(props, option, state) => {
                                const { selected } = state;
                                return <li {...props} key={option.value}>
                                    <Box
                                        component={DoneIcon}
                                        sx={{ width: 17, height: 17, mr: '7px', ml: '-2px' }}
                                        style={{
                                            visibility: selected ? 'visible' : 'hidden',
                                        }}
                                    />
                                    <Box
                                        sx={{
                                            flexGrow: 1,
                                            color: '#586069',
                                        }}
                                    >
                                        {option.value}
                                        <Chip
                                            label="nuevo"
                                            color="success"
                                            variant="filled"
                                            size="small"
                                            sx={{
                                                ml: 1,
                                                visibility: option.isNew ? 'visible' : 'hidden',
                                                color: 'white',
                                                fontSize: '0.6rem',
                                            }}
                                        />
                                    </Box>
                                    <RemoveIconButtonStyled
                                        sx={{ width: 18, height: 18 }}
                                        style={{
                                            visibility: selected ? 'visible' : 'hidden',
                                        }}
                                    >
                                        <RemoveIcon fontSize="small" />
                                    </RemoveIconButtonStyled>
                                    <AddIconButtonStyled
                                        sx={{ width: 18, height: 18 }}
                                        style={{
                                            visibility: !selected ? 'visible' : 'hidden',
                                        }}
                                    >
                                        <AddIcon fontSize="small" />
                                    </AddIconButtonStyled>
                                </li>
                            }}
                            renderTags={(tagValue, getTagProps, { options }) => {
                                return tagValue.map((option, index) => (
                                    <Chip
                                        {...getTagProps({ index })}
                                        key={option.value}
                                        label={option.value}
                                    />
                                ))
                            }}
                            renderInput={(params) => (
                                <>
                                    <TextField
                                        {...params}
                                        label={label}
                                        placeholder={placeholder}
                                        error={errors[name_] ? true : false}
                                        helperText={
                                            <ErrorMessage
                                                name={name_}
                                                errors={errors}
                                                render={({ message }) => <FormValidationMessage message={message} />}
                                            />
                                        }
                                    />
                                </>
                            )}
                            filterOptions={(options, params) => {
                                const filtered = options.filter((option) => {
                                    const value = option.value.toLowerCase();
                                    const inputValue = params.inputValue.toLowerCase();
                                    return value.indexOf(inputValue) !== -1;
                                });

                                const { inputValue } = params;
                                const isExisting = options.some((option) => inputValue === option.value);
                                if (inputValue !== '' && !isExisting) {
                                    filtered.push({
                                        inputValue,
                                        value: `Agregar "${inputValue}"`,
                                        isNew: false
                                    });
                                }

                                return filtered;
                            }}
                            onChange={(event, newValue, reason, details) => {
                                const newValue_ = newValue.map((item: string | OptionItemCreatable) => {
                                    if (typeof item === 'string') {
                                        return {
                                            value: item,
                                            inputValue: item,
                                            isNew: true
                                        }
                                    }
                                    if (item.inputValue !== "") {
                                        return {
                                            inputValue: item.value,
                                            value: item.inputValue,
                                            isNew: true
                                        }
                                    }
                                    return item;
                                });

                                field.onChange(newValue_);
                            }}
                            value={field.value ?? []}
                            noOptionsText="No hay opciones"
                        />
                    )
                }}

            />
            <ErrorMessage
                name={name_}
                errors={errors}
                render={({ message }) => <FormValidationMessage message={message} />}
            />
            {info && (
                <InfoFieldPopper
                    id={id}
                    open={open}
                    anchorEl={anchorEl}
                    info={info}
                    handleClickAway={handleClickAway}
                />
            )}
        </>

    )
}

export default CustomMultiSelectAdd