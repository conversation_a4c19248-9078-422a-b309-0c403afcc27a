import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress';
import React from 'react'

interface RevalidateButtonProps {
    onClick: () => void;
    loading?: boolean;
    children: React.ReactNode;
}
const RevalidateButton = ({ onClick, loading, children }: RevalidateButtonProps) => {
    return (
        <Button
            disabled={loading}
            sx={{
                backgroundColor: "primary.main",
                color: "white",
                borderRadius: 1,
                "&:hover": {
                    backgroundColor: "primary.dark",
                },
                "&:disabled": {
                    backgroundColor: "primary.light",
                },
                minWidth: "80px",
                maxWidth: "80px",
                width: "80px",
                fontSize: "10px",
            }}
            onClick={onClick}
            size="small"
        >
            {loading && <CircularProgress color="inherit" size={20} />}
            {!loading && children}
        </Button>
    )
}

export default RevalidateButton