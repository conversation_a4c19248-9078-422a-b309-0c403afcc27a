"use client"
import HomeIcon from '@mui/icons-material/Home'
import LogoutIcon from '@mui/icons-material/Logout'
import SettingsIcon from '@mui/icons-material/Settings'
import SettingsApplicationsIcon from '@mui/icons-material/SettingsApplications'
import Divider from '@mui/material/Divider'
import Drawer from '@mui/material/Drawer'
import List from '@mui/material/List'
import ListItem from '@mui/material/ListItem'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import Link from 'next/link'
import { useState } from 'react'

const LINKS = [
    { text: 'Home', href: '/', icon: HomeIcon },
    { text: 'Parametros', href: '/parametros', icon: SettingsApplicationsIcon },
];

const PLACEHOLDER_LINKS = [
    { text: 'Configuración', icon: SettingsIcon },
    { text: 'Cerrar sesión', icon: LogoutIcon },
];
const MainDrawer = () => {
    const [open, setOpen] = useState(false);
    const [drawerWith, setDrawerWith] = useState(0);


    return (
        <Drawer
            sx={{
                width: drawerWith,
                flexShrink: 0,
                '& .MuiDrawer-paper': {
                    width: drawerWith,
                    boxSizing: 'border-box',
                    top: ['48px', '56px', '64px'],
                    height: 'auto',
                    bottom: 0,
                },
            }}
            variant="persistent"
            anchor="left"
        >
            <Divider />
            <List>
                {LINKS.map(({ text, href, icon: Icon }) => (
                    <ListItem key={href} disablePadding>
                        <ListItemButton component={Link} href={href}>
                            <ListItemIcon>
                                <Icon />
                            </ListItemIcon>
                            <ListItemText primary={text} />
                        </ListItemButton>
                    </ListItem>
                ))}
            </List>
            <Divider sx={{ mt: 'auto' }} />
            <List>
                {PLACEHOLDER_LINKS.map(({ text, icon: Icon }) => (
                    <ListItem key={text} disablePadding>
                        <ListItemButton>
                            <ListItemIcon>
                                <Icon />
                            </ListItemIcon>
                            <ListItemText primary={text} />
                        </ListItemButton>
                    </ListItem>
                ))}
            </List>
        </Drawer>
    )
}

export default MainDrawer