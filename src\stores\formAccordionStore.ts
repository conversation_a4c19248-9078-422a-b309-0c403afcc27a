import { create } from "zustand";
import { useShallow } from "zustand/react/shallow";

export type FormAccordionItem = { [key: string | number]: boolean };
export interface AccordionGroupState {
    activeIndex: FormAccordionItem | null;
    expandAll: boolean;
    toggleAccordion: (id: number | string, expanded: boolean) => void;
    initialState?: FormAccordionItem;
    setInitialState: (initialState: FormAccordionItem) => void;
};

export const useFormAccordionStore = create<AccordionGroupState>((set) => ({
    activeIndex: null,
    expandAll: false,
    toggleAccordion: (id: number | string, expanded: boolean) => set(
        (state) => ({ activeIndex: { ...state.activeIndex, [id]: expanded } })
    ),
    setInitialState: (initialState: FormAccordionItem) => set({ initialState }),
}));

export const useAccordionActiveIndex = (id: number | string) => {
    const activeIndex = useFormAccordionStore(useShallow((state) => {
        return state.activeIndex ?
            (state.activeIndex[id] ? state.activeIndex[id] : false) : false;
    }));
    return activeIndex;
}