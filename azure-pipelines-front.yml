# Node.js with React
# Build a Node.js project that uses React.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
- master

variables:
- group: FrontPreAprobados-PROD

pool:
  vmImage: ubuntu-latest

steps:
- task: replacetokens@5
  inputs:
    targetFiles: 'next.config.js'
    encoding: 'auto'
    tokenPattern: 'azpipelines'
    writeBOM: true
    actionOnMissing: 'warn'
    keepToken: false
    actionOnNoFiles: 'continue'
    enableTransforms: false
    enableRecursion: false
    useLegacyPattern: false
    enableTelemetry: true
    rootDirectory: $(System.DefaultWorkingDirectory)
- task: CmdLine@2
  inputs:
    script: |
      cat next.config.js
- task: NodeTool@0
  inputs:
    versionSpec: '18.x'
  displayName: 'Install Node.js'

- script: |
    npm install
    npm run build
  displayName: 'npm install and build'

- task: Docker@2
  inputs:
    containerRegistry: 'docker01.bancontinental.com.py'
    repository: '$(registryNamespace)/$(imageName)'
    command: 'buildAndPush'
    # tags: '$(imageVersion)'
    tags: $(Build.SourceVersion)
    Dockerfile: './dockerfile'

- task: helm@1
  inputs:
    subCommand: 'package'
    arguments: './deploy/K8s --app-version $(Build.SourceVersion) -d $(Build.ArtifactStagingDirectory)'

- task: Bash@3
  displayName: cambiar-nombre
  inputs:
    targetType: 'inline'
    script: 'mv *.tgz $(Build.DefinitionName).tgz'
    workingDirectory: '$(Build.ArtifactStagingDirectory)'

- task: PublishBuildArtifacts@1
  inputs:
    PathtoPublish: '$(Build.ArtifactStagingDirectory)'
    ArtifactName: 'drop'
    publishLocation: 'Container'

