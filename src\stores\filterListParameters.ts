import { FilterControlState } from "@/interfaces/filterControls";
import { create } from "zustand";
import { useShallow } from "zustand/react/shallow";

export interface FilterListParametersState {
    sideFilterOpened: boolean;
    filters: FilterControlState[];
    toggleShowSideFilter: () => void;
    setFilters: (filters: FilterControlState[]) => void;
    resetFilters: () => void;
};

export const useFilterListParameters = create<FilterListParametersState>((set) => ({
    sideFilterOpened: false,
    filters: [],
    toggleShowSideFilter: () => set(
        (state) => ({ sideFilterOpened: !state.sideFilterOpened })
    ),
    setFilters: (filters: FilterControlState[]) => set({ filters }),
    resetFilters: () => set(
        (state) => ({ filters: [] })
    ),
}));

export const useFiltersByKey = (key: string) => {
    const activeIndex = useFilterListParameters(useShallow((state) => {
        return state.filters.find(r => r.key);
    }));
    return activeIndex;
}