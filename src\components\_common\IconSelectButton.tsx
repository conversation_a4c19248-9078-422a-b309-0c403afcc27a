import MenuOpenIcon from '@mui/icons-material/MenuOpen';
import { styled } from '@mui/material';
import IconButton from '@mui/material/IconButton';

const SelectIconButtonStyled = styled(IconButton)(({ theme }) => ({
    color: "white",
    backgroundColor: "#1D428A",
    '&:hover': {
        backgroundColor: theme.palette.primary.dark,
    },
}));

interface IconSelectButtonProps {
    onClick?: () => void;
}
const IconSelectButton = ({ onClick }: IconSelectButtonProps) => {
    return (
        <SelectIconButtonStyled
            onClick={onClick}
            size="small"
        >
            <MenuOpenIcon />
        </SelectIconButtonStyled>
    )
}

export default IconSelectButton