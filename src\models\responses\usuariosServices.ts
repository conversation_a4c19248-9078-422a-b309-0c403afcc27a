export interface RolesParametros {
    idRol: number,
    descripcion: string,
    estado: string
}

export interface UpdateAccionesUsuario {
    codigoUsuario: string,
    accionesUsuario: {
        idRolUsuario: number,
        estado: string
    }[]
}

export interface InsertAccionesUsuario {
    codigoUsuario: string,
    accionesUsuario: {
        idRol: number,
        estado: string
    }[]
}

export interface RolesUsuario {
    idRol: number,
    estado: string
}

export interface RolesUsuarioResponse {
    idRolUsuario: number,
    idRol: number,
    codigoUsuario: string,
    estado: string,
    descripcion: string
}