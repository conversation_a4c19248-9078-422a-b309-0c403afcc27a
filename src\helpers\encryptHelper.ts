import { createCipheriv, randomBytes, createDecipheriv } from 'crypto';

const algorithm = 'aes-256-cbc';
const encryptSecretKey = process.env.NEXT_PUBLIC_KEY_AES ?? "";

const key = randomBytes(32);

const encrypt = (message: string) => {
    const cipher = createCipheriv(algorithm, key, key);
    const encrypted = Buffer.concat([cipher.update(message, 'utf8'), cipher.final()]);

    return encrypted.toString('hex');
};

const decrypt = (content: string) => {
    const algorithm = 'aes-256-cbc';
    const key = Buffer.from(encryptSecretKey, 'utf8');
    const decipher = createDecipheriv(algorithm, key, key);
    const decrypted = Buffer.concat([decipher.update(Buffer.from(content, 'hex')), decipher.final()]);

    return decrypted.toString();
};

export const encryptHelper = {
    encrypt,
    decrypt
}