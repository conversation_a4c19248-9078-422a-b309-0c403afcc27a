import { UserPermissionsResponseModel } from "@/models/responses/authServices"
import dayjs from "dayjs";

const hasPermission = (permissions: UserPermissionsResponseModel[]) => {
    // return permissions.some((permission) => {
    //     return permission.url === process.env.NEXT_PUBLIC_VALID_HOST
    // });
    return true
}

const tokenHasExpired = (expiresInSeconds: number) => {
    const now = dayjs().unix();
    const expiresIn = dayjs().add(expiresInSeconds, 'seconds').unix();
    return expiresIn < now
}

export const authHelper = {
    hasPermission,
    tokenHasExpired
}