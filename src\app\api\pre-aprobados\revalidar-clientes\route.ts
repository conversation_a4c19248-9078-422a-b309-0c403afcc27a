import { baseApi, preAprobados<PERSON>pi } from "@/lib/api";
import { preApprovedService } from "@/services/preApprovedService";
import axios from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    data: string,
    error?: string
    status: number
    meta?: any
}
export async function PUT(req: NextRequest) {
    let responseBody: Response = {
        data: "",
        error: undefined,
        status: 200
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 401;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    preAprobadosApi.defaults.nextRequest = req;

    const request = await req.json() as { revalidar: string, periodo: string };

    const params = {
        revalidar: request.revalidar,
        periodo: request.periodo
    }

    try {
        const resp = await preApprovedService.revalidatePreApprovedClients(params);
        responseBody.data = resp?.data;
        return NextResponse.json(responseBody.data, { status: 200 });
    } catch (error: any) {
        responseBody.error = "Error al revalidar los datos";
        responseBody.status = 500;
        responseBody.meta = error;
        if (axios.isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
            responseBody.meta = {
                data: error.config?.data,
                url: error.config?.url,
            };
        }
        console.error(error);
        return NextResponse.json(responseBody, { status: responseBody.status });
    }

}