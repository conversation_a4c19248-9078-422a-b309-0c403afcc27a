import SearchIcon from '@mui/icons-material/Search';
import { CircularProgress, SxProps, Theme } from '@mui/material';
import Button from '@mui/material/Button';

interface SearchButtonProps {
    onClick: (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
    children: React.ReactNode;
    disabled?: boolean;
    isLoading?: boolean;
    sx?: SxProps<Theme>;
    fullWidth?: boolean;
    color?: "inherit" | "primary" | "secondary" | "success" | "error" | "info" | "warning" | undefined;
    icon?: React.ReactNode;
}
const SearchButton = ({
    onClick,
    children,
    sx,
    icon,
    disabled = false,
    isLoading = false,
    fullWidth = false,
    color
}: SearchButtonProps) => {
    return (
        <Button
            variant={"contained"}
            onClick={onClick}
            sx={sx}
            startIcon={!isLoading ? (!icon ? <SearchIcon /> : icon) : <CircularProgress size={20} />}
            disabled={disabled || isLoading}
            fullWidth={fullWidth}
            color={color}
        >
            {children}
        </Button>
    )
}

export default SearchButton