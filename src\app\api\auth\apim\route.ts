import { baseApi } from "@/lib/api";
import { AuthResponseModel } from "@/models/responses/authServices";
import { authServices } from "@/services/authServices";
import { isAxiosError } from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    auth?: AuthResponseModel,
    error?: string
    status: number
    meta?: any
}
export async function POST(req: NextRequest) {
    let responseBody: Response = {
        auth: undefined,
        error: undefined,
        status: 200
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 404;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    baseApi.defaults.nextRequest = req;

    try {
        const resp = await authServices.authApim();
        responseBody.auth = resp.data;
        return NextResponse.json(responseBody.auth, { status: 200 })
    } catch (error: any) {
        console.error(error)
        responseBody.error = "Error al obtener los datos del cliente";
        responseBody.status = 500;
        responseBody.meta = error;
        if (isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
            responseBody.meta = {
                data: error.config?.data,
                url: error.config?.url,
            };
        }
        return NextResponse.json(responseBody, { status: responseBody.status });
    }
    
}