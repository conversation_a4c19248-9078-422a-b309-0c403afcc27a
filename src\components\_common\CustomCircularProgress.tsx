import Box from '@mui/material/Box'
import CircularProgress, { CircularProgressProps, circularProgressClasses } from '@mui/material/CircularProgress'

interface CustomCircularProgressProps extends CircularProgressProps {

}
const CustomCircularProgress = (props: CustomCircularProgressProps) => {
    return (
        <Box sx={{ position: 'relative' }}>
            <CircularProgress
                variant="determinate"
                sx={{
                    color: (theme) =>
                        theme.palette.grey[theme.palette.mode === 'light' ? 200 : 800],
                }}
                size={40}
                thickness={4}
                value={100}
                {...props}
            />
            <CircularProgress
                variant="indeterminate"
                disableShrink
                sx={{
                    color: (theme) => (theme.palette.mode === 'light' ? '#1a90ff' : '#308fe8'),
                    animationDuration: '550ms',
                    position: 'absolute',
                    left: 0,
                    [`& .${circularProgressClasses.circle}`]: {
                        strokeLinecap: 'round',
                    },
                }}
                size={40}
                thickness={4}
                {...props}
            />
        </Box>
    )
}

export default CustomCircularProgress