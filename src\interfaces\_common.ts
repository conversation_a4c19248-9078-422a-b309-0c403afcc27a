import { DeepKeys } from "@tanstack/react-table";

export interface AppCurrency {
    country: string;
    code: string;
    symbol: string;
    thousandSeparator: string;
    decimalScale: number;
    decimalSeparator: string;
}

export interface AppNumberFormat {
    thousandSeparator: string;
    decimalScale: number;
    decimalSeparator: string;
}

export interface AppDateFormat {
    simple: string;
    humanDate: string;
}

export type TValues = Record<string, any>;

export type OptionItem = {
    label: string;
    value: string;
};
export type OptionItemCreatable = {
    value: string;
    inputValue: string;
    isNew: boolean;
};

export type OptionListGridItem = {
    label: string;
    value: string;
    isDeleted?: boolean;
    isNew?: boolean;
};

export interface SearchResponse<TData extends TValues = any> {
    items: OptionListGridItem | OptionListGridItem[];
    data: TData | TData[] | undefined;
}


export type SimpleRowSelectionState = Record<string, boolean>;
export type SimpleRowData = Record<string, any>;

export interface FieldsDataTable<TData> {
    render?: (row: TData) => JSX.Element;
    id?: DeepKeys<TData> | (string & {});
    key: DeepKeys<TData> | (string & {});
    label: string;
    unique?: boolean;
}

export interface HeaderExportMapper<TData extends TValues> {
    header: string;
    accessorFn: (row: TData) => any;
}