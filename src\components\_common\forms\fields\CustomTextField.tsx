"use client"
import { useInfoFieldPopper } from '@/hooks/useInfoFieldPopper';
import { useStatusFieldPopper } from '@/hooks/useStatusFieldPopper';
import { TValues } from '@/interfaces/_common';
import { PreApprovedParametersGroup } from '@/interfaces/parameters';
import { useFieldMetadataByFieldNameFormState, useFormStore } from '@/stores/formStore';
import { ErrorMessage } from '@hookform/error-message';
import { InputAdornment, TextField, TextFieldProps, TextFieldVariants } from '@mui/material';
import { useEffect } from 'react';
import { Controller, get, useFormContext } from 'react-hook-form';
import FormValidationMessage from '../FormValidationMessage';
import InfoFieldIcon from '../InfoFieldIcon';
import InfoFieldPopper from '../InfoFieldPopper';
import StatusFieldIcon from '../StatusFieldIcon';
import StatusFieldPopper from '../StatusFieldPopper';

interface CustomTextFieldProps<TData extends TValues> extends Partial<Omit<TextFieldProps, 'variant' | 'name'>> {
    label: string;
    name: keyof TData;
    groupId: string;
    variant?: TextFieldVariants;
    shouldUnregister?: boolean;
    info?: string;
    isRangeValue?: boolean;
    maxLength?: number;
}
export const CustomTextField = <TData extends TValues>(props: CustomTextFieldProps<TData>) => {
    const { label, name, shouldUnregister, info, groupId, isRangeValue, maxLength, ...rest } = props;
    const { formState: { errors }, setValue, getValues } = useFormContext();

    const grupos = getValues("grupos") as PreApprovedParametersGroup[];
    const index = grupos.findIndex((grupo) => grupo.id === groupId);
    const name_ = `grupos.${index}.parameters.${name as string}`;
    const error_ = get(errors, name_);

    const {
        handleClickInfo,
        anchorEl,
        open,
        id,
        handleClickAway
    } = useInfoFieldPopper(name as string + "info");

    const {
        handleClickInfo: handleClickStatus,
        anchorEl: anchorElStatus,
        open: openStatus,
        id: idStatus,
        handleClickAway: handleClickAwayStatus
    } = useStatusFieldPopper(name as string + "status");

    const addError = useFormStore((state) => state.addError);
    const removeErrors = useFormStore((state) => state.removeError);
    const fieldMetadata = useFieldMetadataByFieldNameFormState(name as string);
    const fieldMetadataStatus = fieldMetadata?.state ?? "approved";
    useEffect(() => {
        const key = name_ as string;
        if (error_) {
            const message = error_.message as string ?? "";
            addError({ groupId, field: key, message });
        } else {
            removeErrors({ groupId, field: key });
        }
    }, [errors]);

    return (
        <>
            <Controller
                name={name_}
                shouldUnregister={shouldUnregister}
                render={({ field, formState }) => (
                    <TextField
                        {...field}
                        {...rest}
                        label={label}
                        error={error_ ? true : false}
                        disabled={fieldMetadataStatus === "pending"}
                        helperText={
                            <ErrorMessage
                                name={name_}
                                errors={errors}
                                render={({ message }) => <FormValidationMessage message={message} />}
                            />
                        }
                        variant={props.variant ?? 'outlined'}
                        InputProps={{
                            ...rest.InputProps,
                            endAdornment: info && !fieldMetadata ? (
                                <InputAdornment position="end">
                                    <InfoFieldIcon onClick={handleClickInfo} />
                                </InputAdornment>)
                                : (
                                    <InputAdornment position="end" >
                                        {info && <InfoFieldIcon onClick={handleClickInfo} />}
                                        {fieldMetadata && !isRangeValue && <StatusFieldIcon onClick={handleClickStatus} status={fieldMetadata.state} />}
                                    </InputAdornment>
                                ),
                        }}
                        inputProps={{ maxLength: maxLength }}

                        FormHelperTextProps={{
                            sx: {
                                marginLeft: 0
                            }
                        }}

                        sx={{
                            width: "100%",
                            "& .MuiOutlinedInput-root": {
                                "& fieldset": {
                                    borderColor: get(formState.dirtyFields, name_) ? "orange" : "rgba(0, 0, 0, 0.23)",
                                }
                            },
                            "& .MuiDisabled": {
                                "& fieldset": {
                                    borderColor: get(formState.dirtyFields, name_) ? "orange" : "rgba(0, 0, 0, 0.23)",
                                }
                            }
                        }}
                    />

                )}
            />
            {info && (
                <InfoFieldPopper
                    id={id}
                    open={open}
                    anchorEl={anchorEl}
                    info={info}
                    handleClickAway={handleClickAway}
                />
            )}

            {fieldMetadata && (
                <StatusFieldPopper
                    id={idStatus}
                    status={fieldMetadata.state}
                    open={openStatus}
                    anchorEl={anchorElStatus}
                    handleClickAway={handleClickAwayStatus}
                />
            )}

        </>


    );
}