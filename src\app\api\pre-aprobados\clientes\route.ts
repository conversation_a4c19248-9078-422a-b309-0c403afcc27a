import { preAprobadosApi } from "@/lib/api";
import { PreApprovedClientsModel } from "@/models/responses/clientServices";
import { preApprovedService } from "@/services/preApprovedService";
import axios from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    client?: any[],
    error?: string
    status: number
    meta?: any
}

export async function GET(req: NextRequest) {
    let responseBody: Response = {
        client: undefined,
        error: undefined,
        status: 200
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 401;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    preAprobadosApi.defaults.nextRequest = req;

    const searchParams = req.nextUrl.searchParams;
    const params = {
        codigoCliente: searchParams.get('codigoCliente'),
        cuenta: searchParams.get('cuenta'),
        nombreCampanha: searchParams.get('nombreCampanha'),
        promedioAcreditacionMin: searchParams.get('promedioAcreditacionMin'),
        promedioAcreditacionMax: searchParams.get('promedioAcreditacionMax'),
        porcentajeEndeudamientoMin: searchParams.get('porcentajeEndeudamientoMin'),
        porcentajeEndeudamientoMax: searchParams.get('porcentajeEndeudamientoMax'),
        montoPrestamoMin: searchParams.get('montoPrestamoMin'),
        montoPrestamoMax: searchParams.get('montoPrestamoMax'),
        edadMin: searchParams.get('edadMin'),
        edadMax: searchParams.get('edadMax'),
        antiguedadMin: searchParams.get('antiguedadMin'),
        antiguedadMax: searchParams.get('antiguedadMax'),
        estadoInformconf: searchParams.get('estadoInformconf'),
        ald: searchParams.get('ald'),
        codeudor: searchParams.get('codeudor'),
        calificacionBCP: searchParams.get('calificacionBCP'),
        estadoBCP: searchParams.get('estadoBCP'),
        calificacionConti: searchParams.get('calificacionConti'),
        referenciaAtraso: searchParams.get('referenciaAtraso'),
        inforcomf: searchParams.get('inforcomf'),
        cantidadPrestamo: searchParams.get('cantidadPrestamo'),
        sucursal: searchParams.get('sucursal'),
        periodo: searchParams.get('periodo'),
        numeroPaginaActual: searchParams.get('numeroPaginaActual'),
        cantidadRegistrosPagina: searchParams.get('cantidadRegistrosPagina'),
        revalidado: searchParams.get('revalidado'),
        fechaRevalidacion: searchParams.get('fechaRevalidacion')
    };

    try {
        const resp = await preApprovedService.getPreApprovedClients(params);
        responseBody.client = resp.data;
        return NextResponse.json(responseBody.client, { status: 200 });
    } catch (error: any) {
        console.error(error);
        responseBody.error = "Error al obtener los datos del cliente";
        responseBody.status = 500;
        if (axios.isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
            responseBody.meta = {
                data: error.config?.data,
                url: error.config?.url,
                test: process.env.NEXT_PUBLIC_BASE_API_URL
            };
        }
        return NextResponse.json(responseBody, { status: responseBody.status });
    }
}