import { authServices } from "@/services/authServices";
import { useQuery } from "@tanstack/react-query";
import { useSession } from 'next-auth/react';

const useQueryAuthApim = (expiresIn: number) => {
    const { status: statusSession } = useSession();
    const {
        isPending,
        isLoading,
        isFetched,
        error,
        data,
        refetch: fetchConsult,
        isRefetching,
        isRefetchError,
    } = useQuery({
        queryKey: ['queryAuthApim'],
        queryFn: async () => {
            const resp = await authServices.internalAuthApim();
            return {
                data: resp?.data,
                error: null
            };
        }, 
        enabled: statusSession == "authenticated",
        retry: false,
        refetchOnMount: true,
        refetchOnWindowFocus: true,
        refetchOnReconnect: false,
        staleTime: 0,
        gcTime: 0,
        refetchInterval: expiresIn * 1000
    });

    return {
        isPending,
        isLoading,
        isFetched,
        error,
        data,
        fetchConsult,
        isRefetching,
        isRefetchError,
    };
}


export {
    useQueryAuthApim
};