import CustomMaterialDataTable from '@/components/_common/dataTable/MaterialReactDataTable/CustomMaterialDataTable';
import { ESearchServices } from '@/constants/utils';
import notify from '@/helpers/notifyHelper';
import { useInfoFieldPopper } from '@/hooks/useInfoFieldPopper';
import { useStatusFieldPopper } from '@/hooks/useStatusFieldPopper';
import { OptionListGridItem, TValues } from '@/interfaces/_common';
import { DataGridOptions } from '@/interfaces/forms';
import { PreApprovedParametersGroup } from '@/interfaces/parameters';
import { useFieldMetadataByFieldNameFormState } from '@/stores/formStore';
import { ErrorMessage } from '@hookform/error-message';
import Add from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import UndoIcon from '@mui/icons-material/Undo';
import { Chip, IconButton, Stack, Typography, styled } from '@mui/material';
import Box from '@mui/material/Box';
import { MRT_ColumnDef } from 'material-react-table';
import React, { useMemo } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import AsyncAutoCompleteDataGrid from '../../Autocomplete/AsyncAutoCompleteDataGrid';
import SearchInputDataGrid from '../../Autocomplete/SearchInputDataGrid';
import InfoLabel from '../../InfoLabel';
import FormValidationMessage from '../FormValidationMessage';
import InfoFieldIcon from '../InfoFieldIcon';
import InfoFieldPopper from '../InfoFieldPopper';
import StatusFieldIcon from '../StatusFieldIcon';
import StatusFieldPopper from '../StatusFieldPopper';

const AddIconButtonStyled = styled(IconButton)(({ theme }) => ({
    color: "white",
    backgroundColor: "#1D428A",
    '&:hover': {
        backgroundColor: theme.palette.primary.dark,
    },
}));
const RemoveIconButtonStyled = styled(IconButton)(({ theme }) => ({
    color: "white",
    backgroundColor: theme.palette.error.main,
    '&:hover': {
        backgroundColor: theme.palette.error.dark,
    },
}));

interface CustomListDataGridProps<TData extends TValues> {
    label: string;
    id: string;
    name: keyof TData;
    groupId: string;
    columns: MRT_ColumnDef<OptionListGridItem>[];
    inputFieldVariant?: "multiple" | "single";
    searchService?: ESearchServices;
    dataGridOptions?: DataGridOptions;
    info?: string;
}
const CustomListDataGrid = <TData extends TValues>({
    label,
    columns,
    name,
    id,
    groupId,
    inputFieldVariant = "multiple",
    searchService,
    dataGridOptions = {
        counterTextSingular: "seleccionado",
        counterTextPlural: "seleccionados",
        inputLabel: "Seleccionado"
    },
    info
}: CustomListDataGridProps<TData>
) => {

    const [searchValue, setSearchValue] = React.useState<OptionListGridItem | null>(null);
    const [selectValue, setSelectValue] = React.useState<OptionListGridItem[]>([]);
    const { formState: { errors }, setValue, getValues } = useFormContext();

    const grupos = getValues("grupos") as PreApprovedParametersGroup[];
    const index = grupos.findIndex((grupo) => grupo.id === groupId);
    const name_ = `grupos.${index}.parameters.${name as string}`;

    const {
        handleClickInfo,
        anchorEl,
        open,
        id: idPopper,
        handleClickAway
    } = useInfoFieldPopper(name as string + "info");

    const {
        handleClickInfo: handleClickStatus,
        anchorEl: anchorElStatus,
        open: openStatus,
        id: idStatus,
        handleClickAway: handleClickAwayStatus
    } = useStatusFieldPopper(name as string + "status");

    const fieldMetadata = useFieldMetadataByFieldNameFormState(name as string);
    const fieldMetadataStatus = fieldMetadata?.state ?? "approved";

    const data = getValues(name_) as OptionListGridItem[];
    
    const removeAction: MRT_ColumnDef<OptionListGridItem> = useMemo(() => {
        return {
            id: "remove-undo",
            header: "",
            accessorKey: "",
            Cell: ({ row }) => {
                const currentData = getValues(name_) as OptionListGridItem[];
                const handleRemove = () => {
                    const item = currentData?.find(x => x.value == row.original.value);
                    if (item) {
                        item.isDeleted = true;
                        setValue(name_, [...currentData]);
                    }
                }

                const handleUndo = () => {
                    const item = currentData?.find(x => x.value == row.original.value);
                    if (item) {
                        item.isDeleted = false;
                        setValue(name_, [...currentData]);
                    }
                }
                return (
                    <>
                        {
                            row.original.isDeleted ? (
                                <AddIconButtonStyled onClick={handleUndo} size={"small"} >
                                    <UndoIcon fontSize="small" />
                                </AddIconButtonStyled>
                            ) : (
                                <RemoveIconButtonStyled onClick={handleRemove} size={"small"} >
                                    <RemoveIcon fontSize="small" />
                                </RemoveIconButtonStyled>
                            )
                        }


                    </>
                );
            },
            enableColumnFilter: false,
            size: 1,
        }
    }, []);

    const columns_ = React.useMemo<MRT_ColumnDef<OptionListGridItem>[]>(() => [removeAction, ...columns], []);

    const handleAdd = () => {
        if (!searchValue) return;
        const exists = data?.find(x => x.value == searchValue?.value);
        if (exists) {
            notify.error("Ya existe el valor seleccionado");
            return;
        }
        const newItem = { ...searchValue, isNew: true };
        if (data) {
            setValue(name_, [newItem, ...data]);
        } else {
            setValue(name_, [newItem]);
        }
        setSearchValue(null);
    };

    const handleMultipleAdd = () => {
        const existingElements: OptionListGridItem[] = [];
        const intertElements: OptionListGridItem[] = [];
        selectValue.forEach(item => {
            const exists = data?.find(x => x.value == item.value);
            if (exists) {
                existingElements.push(item);
            } else {
                intertElements.push({ ...item, isNew: true });
            }
        });
        setValue(name_, [...intertElements, ...data]);
        if (existingElements.length > 0) {
            notify.error(`Ya existen los siguientes valores: ${existingElements.map(x => x.label).join(", ")}`);
        }
        setSelectValue([]);
    }

    const handleChangeSelect = (value: OptionListGridItem[]) => {
        setSelectValue(value);
    }

    const handleCompleteSearch = (data: OptionListGridItem | null) => {
        if (data != null) {
            setSearchValue(data);
        } else {

        }
    }

    const handleClear = () => {
        setSearchValue(null);
    }


    return (<>
        <Stack
            spacing={1}
            sx={{
                boxShadow: "0px 0px 5px 0px rgba(0,0,0,0.45)",
                padding: "1rem",
            }}
        >
            <Typography component="div" sx={{ flexGrow: 1, fontWeight: "600", fontSize: "19px" }}>
                {label}
                {info && <InfoFieldIcon sx={{ marginLeft: "2px" }} onClick={handleClickInfo} />}
                {fieldMetadata && <StatusFieldIcon sx={{ marginLeft: "2px" }} onClick={handleClickStatus} status={fieldMetadata.state} />}
            </Typography>
            <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                flexWrap: "wrap"
            }}>
                {
                    inputFieldVariant == "multiple" && (<>
                        <AsyncAutoCompleteDataGrid
                            label={"Seleccionar"}
                            id={`${id}_select`}
                            searchService={searchService ?? ESearchServices.COMPANY}
                            onChange={handleChangeSelect}
                            value={selectValue}
                            sx={{
                                flex: 1
                            }}
                            disabled={fieldMetadataStatus == "pending"}
                        />
                        <AddIconButtonStyled onClick={handleMultipleAdd} size={"small"} >
                            <Add />
                        </AddIconButtonStyled>
                    </>)
                }

                {
                    inputFieldVariant == "single" && (<>
                        <SearchInputDataGrid
                            label={"Buscar"}
                            sx={{ flex: "1 1 100%" }}
                            searchService={searchService ?? ESearchServices.COMPANY}
                            onComplete={handleCompleteSearch}
                            labelPropsSx={{
                                fontWeight: "400"
                            }}
                            disabled={fieldMetadataStatus == "pending"}
                        />
                        <Box
                            sx={{
                                display: "flex",
                                flex: "1 1 100%",
                                gap: 1,
                                alignItems: "center",
                            }}
                        >
                            <InfoLabel
                                label={dataGridOptions.inputLabel}
                                value={searchValue?.label ?? ""}
                                variant='input-read-only'
                                sx={{ width: "100%", margin: 0 }}
                                valuePropsSx={{ flexGrow: 1, minHeight: "24px" }}
                                labelPropsSx={{
                                    fontWeight: "400"
                                }}
                                clearable={searchValue != null}
                                onClear={handleClear}
                            />
                            <AddIconButtonStyled onClick={handleAdd} size={"small"} disabled={!searchValue}>
                                <Add />
                            </AddIconButtonStyled>
                        </Box>
                    </>)
                }

            </Box>
            <Controller
                name={name_}
                render={({ field }) => (
                    <CustomMaterialDataTable
                        columns={columns_}
                        data={field.value ?? []}
                        enableRowVirtualization
                        enableFilters
                        enableTopToolbar
                        enableDensityToggle={false}
                        enableFullScreenToggle={false}
                        enablePagination={false}
                        muiTableContainerProps={{ sx: { maxHeight: '350px' } }}

                        renderTopToolbarCustomActions={() => {
                            const fieldValue = field.value as OptionListGridItem[] ?? [];
                            const cantidadExcluidos = fieldValue.filter(item => !item.isDeleted).length ?? 0;
                            return (
                                <Box
                                    sx={{ display: 'flex', gap: '1rem', p: '4px' }}
                                >
                                    <Chip
                                        label={`${cantidadExcluidos > 1 ?
                                            `${cantidadExcluidos} ${dataGridOptions.counterTextPlural}`
                                            : `${cantidadExcluidos} ${dataGridOptions.counterTextSingular}`}`}
                                        sx={{
                                            backgroundColor: 'transparent',
                                            fontWeight: 'bold',
                                            fontSize: '14px',
                                            padding: '5px',
                                            border: '1px solid #1D428A',
                                            color: "#1D428A"
                                        }}
                                    />
                                </Box>
                            )
                        }}
                        muiTableBodyRowProps={({ row }) => {
                            return {
                                sx: {
                                    backgroundColor: row.original?.isDeleted ? "#ff4f4f" : "inherit",
                                    "&:hover td": {
                                        backgroundColor: row.original?.isDeleted ? "#ff4f4f" : "inherit",
                                    }
                                }
                            }
                        }}
                        muiTableBodyCellProps={({ row }) => {
                            return {
                                sx: {
                                    padding: "0.7rem",
                                    fontWeight: row.original?.isDeleted ? "bold" : "inherit",
                                    fontStyle: row.original?.isDeleted ? "italic" : "inherit",
                                }
                            }
                        }}
                    />
                )}
            />
            <ErrorMessage
                name={name_}
                errors={errors}
                render={({ message }) => <FormValidationMessage message={message} />}
            />
        </Stack>
        {info && (
            <InfoFieldPopper
                id={idPopper}
                open={open}
                anchorEl={anchorEl}
                info={info}
                handleClickAway={handleClickAway}
            />
        )}

        {fieldMetadata && (
            <StatusFieldPopper
                id={idStatus}
                status={fieldMetadata.state}
                open={openStatus}
                anchorEl={anchorElStatus}
                handleClickAway={handleClickAwayStatus}
            />
        )}
    </>


    )
}

export default CustomListDataGrid