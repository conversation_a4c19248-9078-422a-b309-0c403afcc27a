export interface DepartamentoModel {
    codigo: number;
    descripcion: string;
}

export interface SucursalModel {
    codigo: string;
    descripcion: string;
}

export interface AuthUserModel {
    codigo: string;
    nombre: string;
    sucursal: SucursalModel;
    departamento: DepartamentoModel;
    correo: string;
    nivel: number;
}

export interface AuthResponseModel {
    access_token?: string;
    expires_in?: string;
    refresh_expires_in?: string;
    refresh_token?: string;
    token_type?: string;
    id_token?: string;
    "not-before-policy"?: string;
    session_state?: string;
    scope?: string;
    error?: string;
    error_description?: string;
}

export interface UserPermissionsResponseModel {
    id: number;
    url: string;
}