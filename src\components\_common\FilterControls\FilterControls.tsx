import { DateRangeControlValue, EFilterControlsTypes, FilterControlMetaData, FilterControlState, FilterControlsItem, RadioFilterControlItem, SelectFilterControlItem } from "@/interfaces/filterControls";
import Grid from "@mui/material/Unstable_Grid2";
import React, { useEffect, useState } from "react";
import DateRangeFilterControl from "./DateRangeFilterControl";
import RadioFilterControl from "./RadioFilterControl";
import SelectFilterControl from "./SelectFilterControl";
import TextFieldFilterControl from "./TextFieldFilterControl";

interface FilterControlsProps {
    filters: FilterControlsItem[];
    onChange: (filterState: FilterControlState[], reason?: "onBlur" | "onEnter") => void,
    metaData?: FilterControlMetaData;
}
const FilterControls = ({ filters, onChange, metaData }: FilterControlsProps) => {
    const [filterState, setFilterState] = useState<FilterControlState[]>([]);
    const [reason, setReason] = useState<"onBlur" | "onEnter">("onBlur");

    useEffect(() => {
        onChange(filterState, reason);
    }, [filterState, reason]);

    //HANDLER PARA EL RADIO BUTTON
    const handlerChangeRadio = (key: string) => {
        const currentFilterState = [...filterState];
        const existFilterRadioButton = currentFilterState.find(f => f.type == EFilterControlsTypes.RadioButton);

        if (existFilterRadioButton) {
            existFilterRadioButton.key = key;
            setFilterState(currentFilterState);
        } else {
            setFilterState(state => {
                return [...state, {
                    key: key,
                    value: "",
                    type: EFilterControlsTypes.RadioButton
                }]
            })
        }
    }

    const handlerChangeRadioValue = (value: string) => {
        const currentFilterState = [...filterState];
        const existFilterRadioButton = currentFilterState.find(f => f.type == EFilterControlsTypes.RadioButton);

        if (existFilterRadioButton) {
            existFilterRadioButton.value = value;
            setFilterState(currentFilterState);
        }
    }

    //HANDLER PARA EL SELECT
    const handlerChangeSelect = (key: string) => {
        const currentFilterState = [...filterState];
        const existFilterSelect = currentFilterState.find(f => f.type == EFilterControlsTypes.Select);

        if (existFilterSelect) {
            existFilterSelect.key = key;
            setFilterState(currentFilterState);
        } else {
            setFilterState(state => {
                return [...state, {
                    key: key,
                    value: "",
                    type: EFilterControlsTypes.Select
                }]
            })
        }
    }

    const handlerChangeSelectValue = (value: string, reason: "onBlur" | "onEnter") => {
        const currentFilterState = [...filterState];
        const existFilterSelect = currentFilterState.find(f => f.type == EFilterControlsTypes.Select);

        if (existFilterSelect) {
            existFilterSelect.value = value;
            setFilterState(currentFilterState);
            setReason(reason);
        }
    }

    //HANDLER PARA EL FILTRO DE RANGO DE FECHAS
    const handlerChangeDateRange = (key: string, date: DateRangeControlValue) => {
        const currentFilterState = [...filterState];
        const existFilterDateRange = currentFilterState.find(f => f.type == EFilterControlsTypes.DateRange);

        if (existFilterDateRange) {
            existFilterDateRange.value = date;
            setFilterState(currentFilterState);
        } else {
            setFilterState(state => {
                return [...state, {
                    key: key,
                    value: date,
                    type: EFilterControlsTypes.DateRange
                }]
            })
        }
    }

    //HANDLER PARA EL FILTRO DE SOLO TEXTFIELD
    const handlerChangeTextField = (value: string, key: string) => {
        const currentFilterState = [...filterState];
        const existFilterTextField = currentFilterState.find(f => f.type == EFilterControlsTypes.TextField && f.key == key);

        if (existFilterTextField) {
            existFilterTextField.value = value;
            setFilterState(currentFilterState);
        } else {
            setFilterState(state => {
                return [...state, {
                    key,
                    value,
                    type: EFilterControlsTypes.TextField
                }]
            })
        }
    }

    return (
        <Grid
            container
            spacing={2}
            sx={{
                overflowY: "auto",
                maxHeight: "calc(100% - 40px)",
            }}
        >
            {
                filters.map(({ type, label, control, key }, i) => {
                    return <Grid xs={6} key={key} sx={{}}>
                        {
                            type == EFilterControlsTypes.RadioButton &&
                            <RadioFilterControl label={label} items={control as RadioFilterControlItem[]} onChangeRadio={handlerChangeRadio} onChange={handlerChangeRadioValue} />
                        }
                        {
                            type == EFilterControlsTypes.Select &&
                            <SelectFilterControl
                                label={label}
                                items={control as SelectFilterControlItem[]}
                                onChangeSelect={handlerChangeSelect}
                                onChange={handlerChangeSelectValue}
                            />
                        }

                        {
                            type == EFilterControlsTypes.TextField &&
                            <TextFieldFilterControl label={label} key_={key ?? ""} onChange={handlerChangeTextField} />
                        }
                    </Grid>
                })
            }
            {
                filters.map(({ type, label, key }, i) => {
                    return <React.Fragment key={key ?? i}>
                        {
                            type == EFilterControlsTypes.DateRange &&
                            <DateRangeFilterControl key_={key ?? ""} label={label} onChange={handlerChangeDateRange} />
                        }
                    </React.Fragment>
                })
            }
        </Grid>

    )
}

export default FilterControls;