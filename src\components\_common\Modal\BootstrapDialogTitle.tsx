import CloseIcon from '@mui/icons-material/Close';
import DialogTitle from '@mui/material/DialogTitle';
import IconButton from '@mui/material/IconButton';

interface BootstrapDialogTitleProps {
    id: string;
    children: React.ReactNode;
    onClose?: () => void;
}
const BootstrapDialogTitle = ({ id, children, onClose, ...props }: BootstrapDialogTitleProps) => {
    return (
        <DialogTitle sx={{ m: 0, p: 2 }} {...props}>
            {children}
            {onClose ? (
                <IconButton
                    aria-label="close"
                    onClick={onClose}
                    sx={{
                        position: 'absolute',
                        right: 8,
                        top: 8,
                        color: (theme) => theme.palette.grey[500],
                    }}
                >
                    <CloseIcon />
                </IconButton>
            ) : null}
        </DialogTitle>
    )
}

export default BootstrapDialogTitle