import PendingIcon from '@mui/icons-material/Pending';
import { SxProps, Theme } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import React from 'react';

interface StatusFieldIconProps {
    onClick: (event: React.MouseEvent<HTMLElement>) => void;
    status: "pending" | "approved";
    sx?: SxProps<Theme>;
}
const StatusFieldIcon = ({ onClick, sx, status }: StatusFieldIconProps) => {
    return (
        <IconButton size="small" sx={{ padding: 0, ...sx }} onClick={onClick}>
            {status == "pending" && <PendingIcon color="warning" />}
        </IconButton>
    )
}

export default StatusFieldIcon