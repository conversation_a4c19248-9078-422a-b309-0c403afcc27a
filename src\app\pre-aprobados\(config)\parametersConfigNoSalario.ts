import { COLUMNS_DEFS, OPTIONS_ITEMS } from "@/constants/utils";
import { FormDataType, FormGroup, FormLayout } from "@/interfaces/forms";
import { PreApprovedParameters } from "@/interfaces/parameters";

const grupoCamposGenerales: FormGroup<PreApprovedParameters> = {
    id: "basic_information",
    name: "basic_information",
    label: "GENERALES",
    fields: [
        {
            id: "calificacionSisFinanciero",
            label: "Calificación del sistema Financiero (Banco Continental)",
            name: "calificacionSisFinanciero",
            dataType: FormDataType.MULTI_SELECT_FIELD,
            options: OPTIONS_ITEMS.calificacionSisFinanciero,
        },
        {
            id: "tipoCliente",
            label: "Tipo Cliente",
            name: "tipoCliente",
            dataType: FormDataType.SELECT_FIELD,
            options: OPTIONS_ITEMS.clientTypes,
            infoField: "El Tipo de Cliente es el tipo de cliente que tiene el cliente",
        },
        {
            id: "refAtraso",
            label: "Referencia de atraso",
            name: "refAtraso",
            dataType: FormDataType.MULTI_SELECT_FIELD,
            options: OPTIONS_ITEMS.refAtraso,
        },
        {
            id: "promedioPeriodoAcreditacion",
            label: "Periodo promedio de acreditación",
            name: "promedioPeriodoAcreditacion",
            dataType: FormDataType.NUMBER_FIELD,
            prefixText: "Meses",
            infoField: "Meses para calculo de Promedio.",
        },
        {
            id: "rangoInforcomf",
            label: "Faja en Informconf",
            name: "rangoInforcomf",
            dataType: FormDataType.TEXT_FIELD,
            infoField: "Verifica si tiene faja en informconf",
            maxLength: 1,
        },
        {
            id: "calificacionBcp",
            label: "Calificación en el BCP",
            name: "calificacionBcp",
            dataType: FormDataType.MULTI_SELECT_FIELD,
            options: OPTIONS_ITEMS.bcpRating
        },
        {
            id: "antiguedad",
            label: "Antiguedad Laboral",
            name: "antiguedad",
            dataType: FormDataType.NUMBER_FIELD,
            infoField: "Antiguedad Laboral",
            prefixText: "Meses"
        },
        {
            id: "tieneMora",
            label: "¿Tiene Mora?",
            name: "tieneMora",
            dataType: FormDataType.RADIO_FIELD_GROUP,
            options: [
                { label: "Sí", value: "true" },
                { label: "No", value: "false" },
            ],
        },
        {
            id: "ALDnegativo",
            label: "Exclusión Listas negativas",
            name: "ALDnegativo",
            dataType: FormDataType.SWITCH_FIELD,
            infoField: "Verifica si el cliente tiene listas negativas",
        },
        {
            id: "riesgoOperacionSospechosa",
            label: "Exclusión Riesgo de Operación Sospechosa (ROS)",
            name: "riesgoOperacionSospechosa",
            dataType: FormDataType.SWITCH_FIELD,
        },
        {
            id: "exclusionSucursales",
            label: "Excluir Sucursales",
            name: "exclusionSucursales",
            dataType: FormDataType.LIST_DATA_GRID,
            columns: COLUMNS_DEFS.branchesColumns,
            dataGridOptions: {
                counterTextSingular: "Sucursal",
                counterTextPlural: "Sucursales",
                inputLabel: "Buscar Sucursal",
            },
        },
    ],
}

const formParametersNoSalarioLayout: FormLayout<PreApprovedParameters> = {
    groups: [
        grupoCamposGenerales,
    ]
}

export {
    formParametersNoSalarioLayout
};