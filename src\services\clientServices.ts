import { externalEndpoints, internalEndpoints } from "@/config/endpoints";
import { baseApi, internalApi } from "@/lib/api";
import { ClientModel } from "@/models/responses/clientServices";

const getDataClientByCod = async (clientCode: string) => {
    const response = await baseApi.get<ClientModel>(
        externalEndpoints.clientes.obtenerDatosClientePorCodigo(clientCode)
    );
    return response
}

const getDataClientByDocNumber = async (docNumber: string) => {
    const response = await baseApi.get<ClientModel>(
        externalEndpoints.clientes.obtenerDatosClientePorDocumento(docNumber)
    );
    return response;
}

const getInternalDataClientByCode = async (clientCode: string) => {
    const response = await internalApi.get<ClientModel>(
        internalEndpoints.datos.obtenerDatosCliente,
        {
            params: {
                clientCode,
            }
        }
    );
    return response;

}

const getInternalDataClientByDocNumber = async (docNumber: string) => {
    const response = await internalApi.get<ClientModel>(
        internalEndpoints.datos.obtenerDatosCliente,
        {
            params: {
                docNumber
            }
        }
    );
    return response;

}

export const clientServices = {
    getDataClientByCod,
    getDataClientByDocNumber,
    getInternalDataClientByCode,
    getInternalDataClientByDocNumber
}