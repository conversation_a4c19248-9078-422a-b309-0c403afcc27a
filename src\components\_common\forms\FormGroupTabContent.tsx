"use client"
import Form from '@/app/pre-aprobados/(components)/Form';
import { useActiveTab } from '@/stores/formGroupTabStore';
import Box from '@mui/material/Box';

interface FormGroupTabContentProps {
    groupId: string;
    formLayout: any;
}
const FormGroupTabContent = ({ groupId, formLayout }: FormGroupTabContentProps) => {
    const activeTab = useActiveTab();
    return (
        <div
            role="tabpanel"
            hidden={activeTab !== groupId}
            id={groupId}
            aria-labelledby={`simple-tab-${groupId}`}
        >
            {activeTab === groupId && (
                <Box sx={{ p: 3 }}>
                    <Form
                        groupId={groupId}
                        formParametersLayout={formLayout}
                    />
                </Box>
            )}
        </div>
    );
}

export default FormGroupTabContent