import { FilterControlState } from "@/interfaces/filterControls";
import { create } from "zustand";
import { useShallow } from "zustand/react/shallow";

export interface FilterDataTableState {
    sideFilterOpened: boolean;
    filters: FilterControlState[];
    setFilters: (filters: FilterControlState[]) => void;
    addFilter: (filter: FilterControlState) => void;
};

export const useFilterDataTableStore = create<FilterDataTableState>((set) => ({
    sideFilterOpened: false,
    filters: [],
    setFilters: (filters: FilterControlState[]) => set({ filters }),
    addFilter: (filter: FilterControlState) => set((state) => {
        return { filters: [...state.filters, filter] };
    }),
}));

export const useFiltersByKey = (key: string) => {
    const activeIndex = useFilterDataTableStore(useShallow((state) => {
        return state.filters.find(r => r.key);
    }));
    return activeIndex;
}