const externalEndpoints = {
    auth: {
        apim: "/autenticarServicio/v1/realms/interno",
        obtenerDatosUsuario: (userId: string) => `/api-usuarios/v1/usuarios/${userId}`,
        obtenerPermisosUsuario: (userCode: string) => `/finansys-admin/v1/usuarios/${userCode}/permisos/urls?aplicacion=FSYS-WEB`
    },
    preAprobados: {
        obtenerClientePreAprobados: (queryParams: string ) => `/api-pre-aprobados/v1/creditos/preaprobados?${queryParams}`,
        obtenerClientePreAprobadosHistorial: (queryParams: string) => `/api-pre-aprobados/v1/creditos/preaprobados/historico?${queryParams}`,
        obtenerClientePreAprobadosTC: (periodo: string) => `/api-pre-aprobados/v1/preApprovedTc/${periodo}`,
        revalidarClientePreAprobados : (queryParams: string ) => `/api-pre-aprobados/v1/creditos/preaprobados/revalidar-datos?${queryParams}`,
        revalidarBcpClientePreAprobados: "/api-pre-aprobados/v1/creditos/preaprobados/clientes/bcp?bcp=SI",
        revalidarEquifaxClientePreAprobados: "/api-pre-aprobados/v1/creditos/preaprobados/clientes/equifax?equifax=SI",
        obtenerParametros: (estado: string) => `/api-pre-aprobados/v1/creditos/preaprobados/parametros?estado=${estado}`,
        actualizarParametros: (userCode: string) => `/api-pre-aprobados/v1/creditos/preaprobados/parametros?usuario=${userCode}`,
        obtenerAcciones: "/api-pre-aprobados/v1/creditos/preaprobados/acciones",
        accionesUsuario: "/api-pre-aprobados/v1/creditos/preaprobados/usuario/acciones",
        getAccionesUsuario: (codigoUsuario: string) => `/api-pre-aprobados/v1/creditos/preaprobados/usuario/acciones?codigoUsuario=${codigoUsuario}`,
        obtenerParametrosHistoricos: () => `/api-pre-aprobados/v1/parametros-preaprobados/historicos`,
    },
    clientes: {
        obtenerDatosClientePorCodigo: (codigoCliente: string) => `/interno/clientes/datos/v1/clientes/${codigoCliente}`,
        obtenerDatosClientePorDocumento: (numeroDocumento: string) => `/interno/clientes/datos/v1/clientes/datos?NumeroDocumento=${numeroDocumento}`,
    },
    comunes: {
        obtenerSucursales: "/finansys/utilitarios/v1/sucursales",
    },
    usuarios: {
        consultarUsuarioPorDominio: (usuarioDominio: string) => `/api-usuarios/v1/usuarios/${usuarioDominio}`,
    }
}

const internalEndpoints = {
    auth: {
        apim: "/auth/apim",
        obtenerDatosUsuario: "/auth/user",
        obtenerPermisosUsuario: `/auth/permissions`,
    },
    datos: {
        obtenerDatosCliente: "/client",
        obtenerSucursales: "/branches",
    },
    preAprobados: {
        obtenerClientesPreAprobados: "/pre-aprobados/clientes",
        obtenerClientesPreAprobadosHistorial: `/pre-aprobados/historial-clientes`,
        obtenerClientePreAprobadosTC: "/pre-aprobados/tc-clientes",
        revalidarClientePreAprobados: "/pre-aprobados/revalidar-clientes",
        revalidarBcpClientePreAprobados: "/pre-aprobados/revalidar-bcp",
        revalidarEquifaxClientePreAprobados: "/pre-aprobados/revalidar-equifax",
        obtenerParametros: "/pre-aprobados/parametros",
        actualizarParametros: "/pre-aprobados/actualizar-parametros",
        obtenerAcciones: "/pre-aprobados/acciones",
        updateAccionesUsuario: "/pre-aprobados/updateAccionesUsuario",
        insertAccionesUsuario: "/pre-aprobados/insertAccionesUsuario",
        getAccionesUsuario: "/pre-aprobados/getAccionesUsuario",
    },
    usuarios: {
        consultarUsuarioPorDominio: "/usuarios/consultarUsuarioPorDominio",
    }
};

export {
    externalEndpoints,
    internalEndpoints
}