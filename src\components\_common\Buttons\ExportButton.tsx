import Button from '@mui/material/Button'
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { SxProps, Theme } from '@mui/material';

interface ExportButtonProps {
    onClick: (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
    children: React.ReactNode;
    disabled?: boolean;
    fullWidth?: boolean;
    sx?: SxProps<Theme>;
}
const ExportButton = ({ onClick, children, disabled = false, fullWidth = false, sx, ...props }: ExportButtonProps) => {
    return (
        <Button
            variant={"contained"}
            onClick={onClick}
            disabled={disabled} {...props}
            startIcon={<FileDownloadIcon />}
            fullWidth={fullWidth}
            sx={sx}
        >
            {children}
        </Button>
    )
}

export default ExportButton