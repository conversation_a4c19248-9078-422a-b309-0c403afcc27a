import { externalEndpoints, internalEndpoints } from "@/config/endpoints";
import { internalApi, baseApi, preAprobadosApi } from "@/lib/api";
import { PreApprovedClientsModel, TCClientsModel } from "@/models/responses/clientServices";
import { ParametersModel } from "@/models/responses/parameterService";
import { ParametersHistoricModel } from "@/models/responses/parameterHistoricServices";
import dayjs from "dayjs";

const getParameters = async (groupId: string, status: "approved" | "pending") => {
    const param = status == "approved" ? "APROBADO" : "PENDIENTE"
    const response = await preAprobadosApi.get<ParametersModel[]>(
        externalEndpoints.preAprobados.obtenerParametros(param));
    return response;
}

const getHistoricParameters = async () => {
    const response = await preAprobadosApi.get<ParametersHistoricModel[]>(
        externalEndpoints.preAprobados.obtenerParametrosHistoricos());
    return response;
}

const updateParameter = async (parameters: ParametersModel[], userCode: string) => {
    const response = await preAprobadosApi.post<boolean>(
        externalEndpoints.preAprobados.actualizarParametros(userCode),
        parameters
    );
    return response;
}

const getInternalParameters = async (groupId: string, status: "approved" | "pending") => {
    const response = await internalApi.get<ParametersModel[]>(
        internalEndpoints.preAprobados.obtenerParametros,
        {
            params: {
                groupId,
                status
            }
        }
    );
    return response;
}

const updateInternalParameters = async (parameters: ParametersModel[], userCode: string) => {
    const response = await internalApi.post<boolean>(
        internalEndpoints.preAprobados.actualizarParametros,
        {
            userCode,
            parameters
        }
    );
    return response;
}

const getPreApprovedClients = async (params: any) => {
    const queryParams = {
        ...params,
        numeroPaginaActual: params.numeroPaginaActual ?? '0',
        cantidadRegistrosPagina: params.cantidadRegistrosPagina ?? '0'
    };

    const queryString = new URLSearchParams(queryParams).toString();

    const response = await preAprobadosApi.get<PreApprovedClientsModel[]>(externalEndpoints.preAprobados.obtenerClientePreAprobados(queryString));
    return response;
}

const getPreApprovedTc = async (month?: string | null, year?: string | null) => {
    const month_ = month ? month : dayjs().add(-1, "month").format("MM");
    const year_ = year ? year : dayjs().add(-1, "month").format("YYYY");
    const periodo = `${year_}${month_}`;
    const response = await preAprobadosApi.get<TCClientsModel[]>(externalEndpoints.preAprobados.obtenerClientePreAprobadosTC(periodo));
    return response;
}

const revalidatePreApprovedClients = async (params: any) => {
    const queryParams = {
        ...params
    };
    const queryString = new URLSearchParams(queryParams).toString();
    const response = await preAprobadosApi.put<string>(
        externalEndpoints.preAprobados.revalidarClientePreAprobados(queryString)
    );
    return response;
}

const revalidateBcpPreApprovedClients = async (datosPreAprobados: any[]) => {
    const response = await preAprobadosApi.put<boolean>(
        externalEndpoints.preAprobados.revalidarBcpClientePreAprobados, datosPreAprobados
    );
    return response;
}

const revalidateEquifaxPreApprovedClients = async () => {
    const response = await baseApi.put<boolean>(
        externalEndpoints.preAprobados.revalidarEquifaxClientePreAprobados
    );
    return response;
}

const getPreApprovedClientsHistory = async (params: any) => {
    const queryParams = {
        ...params,
        numeroPaginaActual: params.numeroPaginaActual ?? '0',
        cantidadRegistrosPagina: params.cantidadRegistrosPagina ?? '0'
    };

    const queryString = new URLSearchParams(queryParams).toString();

    const response = await preAprobadosApi.get<PreApprovedClientsModel[]>(
        externalEndpoints.preAprobados.obtenerClientePreAprobadosHistorial(queryString));
    return response;
};

const getInternalPreApprovedClients = async (params: {}) => {
    const response = await internalApi.get<PreApprovedClientsModel[]>(
        internalEndpoints.preAprobados.obtenerClientesPreAprobados,
        {
            params
        }
    );
    return response;
}

const getInternalPreApprovedClientsHistory = async (params: {}) => {
    const response = await internalApi.get<PreApprovedClientsModel[]>(
        internalEndpoints.preAprobados.obtenerClientesPreAprobadosHistorial,
        {
            params
        }
    );
    return response;
}

const getInternalPreApprovedTc = async (month?: string, year?: string) => {
    const month_ = month ? month : dayjs().add(-1, "month").format("MM");
    const year_ = year ? year : dayjs().add(-1, "month").format("YYYY");
    const periodo = `${year_}${month_}`;
    const response = await internalApi.get<TCClientsModel[]>(
        internalEndpoints.preAprobados.obtenerClientePreAprobadosTC,
        {
            params: {
                month: month_,
                year: year_
            }
        }
    );
    return response;
}

const internalRevalidatePreApprovedClients = async (params: any) => {
    const response = await internalApi.put<string>(
        internalEndpoints.preAprobados.revalidarClientePreAprobados, params
    );
    return response;
}

const internalRevalidateBcpPreApprovedClients = async (datosPreAprobados: any[]) => {
    const response = await internalApi.put<boolean>(
        internalEndpoints.preAprobados.revalidarBcpClientePreAprobados, {
            datosPreAprobados
        }
    );
    return response;
}

const internalRevalidateEquifaxPreApprovedClients = async () => {
    const response = await internalApi.put<boolean>(
        internalEndpoints.preAprobados.revalidarEquifaxClientePreAprobados
    );
    return response;
}

const preApprovedService = {
    updateParameter,
    getParameters,
    getPreApprovedClients,
    getPreApprovedClientsHistory,
    getPreApprovedTc,
    revalidatePreApprovedClients,
    revalidateBcpPreApprovedClients,
    revalidateEquifaxPreApprovedClients,
    getInternalParameters,
    updateInternalParameters,
    getInternalPreApprovedClients,
    getInternalPreApprovedClientsHistory,
    getInternalPreApprovedTc,
    internalRevalidatePreApprovedClients,
    internalRevalidateBcpPreApprovedClients,
    internalRevalidateEquifaxPreApprovedClients,
    getHistoricParameters,
}

export {
    preApprovedService
};