import { utilHelper } from "@/helpers/utilHelper";
import { preAprobadosApi } from "@/lib/api";
import { ParametersModel } from "@/models/responses/parameterService";
import { preApprovedService } from "@/services/preApprovedService";
import axios from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    parameters: ParametersModel[] | undefined,
    error?: string
    status: number
    meta?: any
}
export async function GET(req: NextRequest) {
    let responseBody: Response = {
        parameters: undefined,
        error: undefined,
        status: 200
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 401;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    preAprobadosApi.defaults.nextRequest = req;

    const groupId = req.nextUrl.searchParams.get("groupId");
    const status = req.nextUrl.searchParams.get("status");

    const valid = utilHelper.validateParams([groupId, status]);

    if (!valid) {
        responseBody.error = "Los parametros groupId y status son requeridos";
        responseBody.status = 400;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }

    try {
        const resp = await preApprovedService.getParameters(groupId as string, status as "approved" | "pending");
        responseBody.parameters = resp.data;
        return NextResponse.json(responseBody.parameters, { status: 200 });
    } catch (error: any) {
        responseBody.error = "Error al obtener los parametros";
        responseBody.status = 500;
        responseBody.meta = error;
        if (axios.isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
            responseBody.meta = {
                data: error.config?.data,
                url: error.config?.url,
            };
        }
        console.error(error);
        return NextResponse.json(responseBody, { status: responseBody.status });
    }
}