import { externalEndpoints, internalEndpoints } from "@/config/endpoints";
import { baseApi, internalApi, preAprobadosApi } from "@/lib/api";
import { AuthUserModel } from "@/models/responses/authServices";
import { InsertAccionesUsuario, RolesParametros, RolesUsuarioResponse, UpdateAccionesUsuario } from "@/models/responses/usuariosServices";

const getUsuarioPorDominioInterno = async (usuarioDominio: string) => {
    const resp = await internalApi.get<AuthUserModel>(internalEndpoints.usuarios.consultarUsuarioPorDominio,
        {
            params:{
                usuarioDominio
            }
        });

    return resp;
}

const getAccionesInterno = async () => {
    const resp = await internalApi.get<RolesParametros[]>(internalEndpoints.preAprobados.obtenerAcciones);
    return resp;
}

const getAccionesUsuarioInterno = async (codigoUsuario: string) => {
    const resp = await internalApi.get<RolesUsuarioResponse[]>(internalEndpoints.preAprobados.getAccionesUsuario,
    {
        params: {
            codigoUsuario
        }
    });
    return resp;
}

const updateAccionesUsuarioInterno = async (request: UpdateAccionesUsuario) => {
    const resp = await internalApi.post<string>(internalEndpoints.preAprobados.updateAccionesUsuario,
        {
            request
        }
    );
    return resp;
}

const insertAccionesUsuarioInterno = async (request: InsertAccionesUsuario) => {
    const resp = await internalApi.post<string>(internalEndpoints.preAprobados.insertAccionesUsuario,
        {
            request
        }
    );
    return resp;
}

const getUsuarioPorDominio = async (usuarioDominio: string) => {
    const resp = await baseApi.get<AuthUserModel>(externalEndpoints.usuarios.consultarUsuarioPorDominio(usuarioDominio));
    return resp;
}

const getAcciones = async () => {
    const resp = await preAprobadosApi.get<RolesParametros[]>(externalEndpoints.preAprobados.obtenerAcciones);
    return resp;
}

const getAccionesUsuario = async (codigoUsuario: string | null) => {
    const resp = await preAprobadosApi.get<RolesUsuarioResponse[]>(externalEndpoints.preAprobados.getAccionesUsuario(codigoUsuario ?? ""));
    return resp;
}

const updateAccionesUsuario = async (request: UpdateAccionesUsuario) => {
    const resp = await preAprobadosApi.put<string>(externalEndpoints.preAprobados.accionesUsuario, request);
    return resp;
}

const insertAccionesUsuario = async (request: InsertAccionesUsuario) => {
    const resp = await preAprobadosApi.post<string>(externalEndpoints.preAprobados.accionesUsuario, request);
    return resp;
}

export const usuarioServices = {
    getAccionesInterno,
    getUsuarioPorDominioInterno,
    getAccionesUsuarioInterno,
    updateAccionesUsuarioInterno,
    insertAccionesUsuarioInterno,
    getAcciones,
    getAccionesUsuario,
    updateAccionesUsuario,
    insertAccionesUsuario,
    getUsuarioPorDominio
}