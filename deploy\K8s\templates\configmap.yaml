apiVersion: v1 # La versión de la API de Kubernetes que se está usando para crear este objeto
kind: ConfigMap # Un configmap es un objeto de la API utilizado para almacenar datos no confidenciales en el formato clave-valor
metadata: # Datos que permiten identificar unívocamente al objeto, incluyendo una cadena de texto para el name, UID, y opcionalmente el namespace
  name: {{ .Values.releaseName }}-env # nombre del config
  labels: # etiquetas creadas para un mejor filtro en kubernetes
    app: {{ .Values.releaseName }}-app 
    service: {{ .Values.releaseName }}
data:
  # lista de variables de entorno a crearse en el contenedor, las siguientes variables son solo de referencia
  NEXT_PUBLIC_VALID_HOST: "{{ .Values.config.NEXT_PUBLIC_VALID_HOST }}"
  NEXT_PUBLIC_BASE_API_URL: "{{ .Values.config.NEXT_PUBLIC_BASE_API_URL }}"
  NEXT_PUBLIC_URL_REDIRECT: "{{ .Values.config.NEXT_PUBLIC_URL_REDIRECT }}"
  NEXT_PUBLIC_API_TIME_OUT: "{{ .Values.config.NEXT_PUBLIC_API_TIME_OUT }}"
  NEXT_PUBLIC_KEYCLOAK_ID: "{{ .Values.config.NEXT_PUBLIC_KEYCLOAK_ID }}"
  NEXT_PUBLIC_KEYCLOAK_SECRET: "{{ .Values.config.NEXT_PUBLIC_KEYCLOAK_SECRET }}"
  NEXT_PUBLIC_SUBSCRIPTION_KEY: "{{ .Values.config.NEXT_PUBLIC_SUBSCRIPTION_KEY }}"
  NEXT_PUBLIC_KEYCLOAK_GRANT_TYPE: "{{ .Values.config.NEXT_PUBLIC_KEYCLOAK_GRANT_TYPE }}"
  NEXT_PUBLIC_KEYCLOAK_ISSUER: "{{ .Values.config.NEXT_PUBLIC_KEYCLOAK_ISSUER }}"
  NEXTAUTH_URL: "{{ .Values.config.NEXTAUTH_URL }}"
  NEXTAUTH_SECRET: "{{ .Values.config.NEXTAUTH_SECRET }}"
  NEXT_PUBLIC_KEY_AES: "{{ .Values.config.NEXT_PUBLIC_KEY_AES }}"