{"name": "front-preaprobados", "version": "0.1.0", "private": true, "scripts": {"dev": "env-cmd -f .env.development.local next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.3.2", "@mui/base": "^5.0.0-beta.40", "@mui/icons-material": "^5.14.18", "@mui/material": "^5.14.18", "@tanstack/react-query": "^5.13.4", "@tanstack/react-table": "^8.10.7", "axios": "^1.6.2", "dayjs": "^1.11.10", "env-cmd": "^10.1.0", "export-from-json": "^1.7.3", "material-react-table": "^2.0.5", "next": "14.0.3", "next-auth": "^4.24.5", "notistack": "^3.0.1", "ramda": "^0.30.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "react-number-format": "^5.3.1", "xlsx": "^0.18.5", "yup": "^1.3.2", "zustand": "^4.4.7"}, "devDependencies": {"@faker-js/faker": "^8.3.1", "@types/node": "^20", "@types/ramda": "^0.30.0", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.0.3", "typescript": "^5"}}