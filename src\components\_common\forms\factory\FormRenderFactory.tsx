import { TValues } from '@/interfaces/_common'
import { FormDataType, FormField } from '@/interfaces/forms'
import { InputAdornment } from '@mui/material'
import Grid from '@mui/material/Unstable_Grid2'
import { NumberCurrencyFormat } from '../../NumberCurrencyFormat'
import { NumberFormat } from '../../NumberFormat'
import CustomListDataGrid from '../fields/CustomListDataGrid'
import CustomMultiSelect from '../fields/CustomMultiSelect'
import CustomMultiSelectAdd from '../fields/CustomMultiSelectAdd'
import { CustomRadioGroup } from '../fields/CustomRadioGroup'
import CustomRangeCurrency from '../fields/CustomRangeCurrency'
import CustomRangeNumber from '../fields/CustomRangeNumber'
import CustomSelect from '../fields/CustomSelect'
import CustomSwitch from '../fields/CustomSwitch'
import { CustomTextField } from '../fields/CustomTextField'
import HiddenGroupField from '../fields/HiddenGroupField'

interface FormRenderFactoryProps<TData extends TValues> {
    fields: FormField<TData>[];
    groupId: string;
}
const FormRenderFactory = <TData extends TValues>({ fields, groupId }: FormRenderFactoryProps<TData>) => {
    return (
        <Grid
            container
            spacing={2}
            rowGap={3}
        >
            {fields.map((field) => {
                const {
                    id,
                    label,
                    name,
                    dataType,
                    xs,
                    infoField,
                    shouldUnregister,
                    variant,
                    prefixText,
                    subfixText,
                    placeholder,
                    options,
                    columns,
                    inputFieldVariant,
                    searchService,
                    dataGridOptions,
                    rangeOptions,
                    maxLength
                } = field
                return (
                    <Grid
                        xs={xs ?? 6}
                        key={id}
                    >
                        {dataType === FormDataType.TEXT_FIELD && (
                            <CustomTextField<TData>
                                id={id}
                                name={name}
                                placeholder={placeholder}
                                label={label}
                                variant={variant}
                                shouldUnregister={shouldUnregister}
                                sx={{
                                    width: '100%'
                                }}
                                info={infoField}
                                groupId={groupId}
                                maxLength={maxLength}
                            />
                        )}

                        {dataType === FormDataType.NUMBER_FIELD && (
                            <CustomTextField<TData>
                                id={id}
                                name={name}
                                label={label}
                                placeholder={placeholder}
                                info={infoField}
                                groupId={groupId}
                                required
                                InputProps={{
                                    startAdornment: prefixText && <InputAdornment position="start">{prefixText}</InputAdornment>,
                                    endAdornment: subfixText && <InputAdornment position="end">{subfixText}</InputAdornment>,
                                    inputComponent: NumberFormat as any,
                                }}
                                shouldUnregister={shouldUnregister}
                                sx={{
                                    width: '100%'
                                }}
                            />
                        )}

                        {dataType === FormDataType.SWITCH_FIELD && (
                            <CustomSwitch<TData>
                                id={id}
                                name={name}
                                label={label}
                                info={infoField}
                                groupId={groupId}
                                required
                            />
                        )}

                        {dataType === FormDataType.RADIO_FIELD_GROUP && (
                            <CustomRadioGroup<TData>
                                id={id}
                                name={name}
                                label={label}
                                options={options ?? []}
                                info={infoField}
                                groupId={groupId}
                            />
                        )}

                        {dataType === FormDataType.SELECT_FIELD && (
                            <CustomSelect<TData>
                                id={id}
                                name={name}
                                label={label}
                                placeholder={placeholder}
                                options={options ?? []}
                                optionSelector={"label"}
                                info={infoField}
                                groupId={groupId}
                            />
                        )}

                        {dataType === FormDataType.MULTI_SELECT_CREATABLE_FIELD && (
                            <CustomMultiSelectAdd<TData>
                                id={id}
                                name={name}
                                label={label}
                                placeholder={placeholder}
                                info={infoField}
                                groupId={groupId}
                            />
                        )}

                        {dataType === FormDataType.MULTI_SELECT_FIELD && (
                            <CustomMultiSelect
                                id={id}
                                name={name}
                                label={label}
                                placeholder={placeholder}
                                options={options ?? []}
                                groupId={groupId}
                                info={infoField}
                            />
                        )}

                        {dataType === FormDataType.CURRENCY_FIELD && (
                            <CustomTextField<TData>
                                id={id}
                                name={name}
                                label={label}
                                placeholder={placeholder}
                                info={infoField}
                                groupId={groupId}
                                required
                                InputProps={{
                                    startAdornment: prefixText && <InputAdornment position="start">{prefixText}</InputAdornment>,
                                    endAdornment: subfixText && <InputAdornment position="end">{subfixText}</InputAdornment>,
                                    inputComponent: NumberCurrencyFormat as any,
                                }}
                                shouldUnregister={shouldUnregister}
                                sx={{
                                    width: '100%'
                                }}
                            />
                        )}

                        {dataType === FormDataType.LIST_DATA_GRID && (
                            <CustomListDataGrid<TData>
                                label={label}
                                name={name}
                                columns={columns ?? []}
                                id={id}
                                groupId={groupId}
                                inputFieldVariant={inputFieldVariant}
                                searchService={searchService}
                                dataGridOptions={dataGridOptions}
                            />
                        )}

                        {dataType === FormDataType.RANGE_CURRENCY_VALUE && (
                            <CustomRangeCurrency<TData>
                                label={label}
                                name={name}
                                rangeOptions={rangeOptions ?? []}
                                prefixText={prefixText}
                                subfixText={subfixText}
                                groupId={groupId}
                                info={infoField}
                            />
                        )}

                        {dataType === FormDataType.RANGE_NUMBER_VALUE && (
                            <CustomRangeNumber<TData>
                                name={name}
                                label={label}
                                rangeOptions={rangeOptions ?? []}
                                prefixText={prefixText}
                                subfixText={subfixText}
                                groupId={groupId}
                                info={infoField}
                            />
                        )}
                    </Grid>
                )
            })}

            <HiddenGroupField groupId={groupId} />
        </Grid>
    )
}

export default FormRenderFactory