import { PreApprovedParametersGroup } from '@/interfaces/parameters';
import React from 'react'
import { Controller, useFormContext } from 'react-hook-form';

interface HiddenFieldProps {
    groupId: string;
}
const HiddenGroupField = ({ groupId }: HiddenFieldProps) => {
    const { getValues, formState: { errors } } = useFormContext();
    const grupos = getValues("grupos") as PreApprovedParametersGroup[];
    const index = grupos.findIndex((grupo) => grupo.id === groupId);
    const name_ = `grupos.${index}.id`;

    return (
        <Controller
            name={name_}
            render={({ field }) => (
                <input
                    type="hidden"
                    {...field}
                />
            )}
        />
    )
}

export default HiddenGroupField