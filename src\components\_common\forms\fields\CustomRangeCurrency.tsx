"use client"
import { useInfoFieldPopper } from '@/hooks/useInfoFieldPopper';
import { useStatusFieldPopper } from '@/hooks/useStatusFieldPopper';
import { TValues } from '@/interfaces/_common';
import { RangeFieldOptions } from '@/interfaces/forms';
import { ParameterMetadata } from '@/interfaces/parameters';
import { useFieldMetadataByFieldNameFormState } from '@/stores/formStore';
import { ErrorMessage } from '@hookform/error-message';
import { Box, InputAdornment, Paper, TextFieldProps, TextFieldVariants, Typography } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { NumberCurrencyFormat } from '../../NumberCurrencyFormat';
import FormValidationMessage from '../FormValidationMessage';
import InfoFieldIcon from '../InfoFieldIcon';
import InfoFieldPopper from '../InfoFieldPopper';
import StatusFieldIcon from '../StatusFieldIcon';
import Status<PERSON>ieldPopper from '../StatusFieldPopper';
import { CustomTextField } from './CustomTextField';

interface CustomRangeCurrencyProps<TData extends TValues> extends Partial<Omit<TextFieldProps, 'variant' | 'name'>> {
    label: string;
    name: keyof TData;
    rangeOptions: RangeFieldOptions[];
    prefixText?: string;
    subfixText?: string;
    variant?: TextFieldVariants;
    shouldUnregister?: boolean;
    groupId: string;
    info?: string;
}
const CustomRangeCurrency = <TData extends TValues>(props: CustomRangeCurrencyProps<TData>) => {
    const {
        label,
        name,
        shouldUnregister,
        prefixText,
        subfixText,
        rangeOptions,
        groupId,
        info,
        ...rest
    } = props;
    const { formState: { errors }, setValue, getValues } = useFormContext();

    const name_ = name as string;

    const {
        handleClickInfo,
        anchorEl,
        open,
        id,
        handleClickAway
    } = useInfoFieldPopper(name as string + "info");

    const {
        handleClickInfo: handleClickStatus,
        anchorEl: anchorElStatus,
        open: openStatus,
        id: idStatus,
        handleClickAway: handleClickAwayStatus
    } = useStatusFieldPopper(name as string + "status");

    const fieldMetadataRangeOne = useFieldMetadataByFieldNameFormState(rangeOptions[0].name) ?? {} as ParameterMetadata;
    const fieldMetadataRangeTwo = useFieldMetadataByFieldNameFormState(rangeOptions[1].name) ?? {} as ParameterMetadata;

    const fieldsMetadata: ParameterMetadata[] = [fieldMetadataRangeOne, fieldMetadataRangeTwo];
    const fieldMetadataStatus = fieldsMetadata.every(i => i.state == "approved") ? "approved" : "pending";
    return (
        <Box
            component={Paper}
            sx={{
                display: 'flex',
                flexDirection: 'column',
                padding: '16px',
            }}
        >
            <Typography
                sx={{
                    marginBottom: '16px',
                    color: "black",
                    fontWeight: '500',
                    fontSize: '16px',
                }}
            >
                {label}
                {info && <InfoFieldIcon sx={{ marginLeft: "3px" }} onClick={handleClickInfo} />}
                {fieldsMetadata && <StatusFieldIcon sx={{ marginLeft: "3px" }} onClick={handleClickStatus} status={fieldMetadataStatus} />}
            </Typography>
            <Box
                sx={{
                    display: 'flex',
                    gap: 1,
                    width: '100%'
                }}
            >
                {
                    rangeOptions.map((rangeOption, index) => (
                        <CustomTextField<TData>
                            key={rangeOption.id}
                            id={rangeOption.id}
                            name={rangeOption.name}
                            groupId={groupId}
                            label={rangeOption.label}
                            placeholder={rangeOption.placeholder}
                            InputProps={{
                                startAdornment: prefixText && <InputAdornment position="start">{prefixText}</InputAdornment>,
                                endAdornment: subfixText && <InputAdornment position="end">{subfixText}</InputAdornment>,
                                inputComponent: NumberCurrencyFormat as any
                            }}
                            shouldUnregister={shouldUnregister}
                            sx={{
                                flex: 1
                            }}
                            isRangeValue={true}
                        />
                    ))
                }
            </Box>
            <ErrorMessage
                name={name_}
                errors={errors}
                render={({ message }) => <FormValidationMessage message={message} />}
            />
            {info && (
                <InfoFieldPopper
                    id={id}
                    open={open}
                    anchorEl={anchorEl}
                    info={info}
                    handleClickAway={handleClickAway}
                />
            )}

            {fieldsMetadata && (
                <StatusFieldPopper
                    id={idStatus}
                    status={fieldMetadataStatus}
                    open={openStatus}
                    anchorEl={anchorElStatus}
                    handleClickAway={handleClickAwayStatus}
                />
            )}
        </Box>


    );
}

export default CustomRangeCurrency;