import notify from '@/helpers/notifyHelper';
import { utilHelper } from '@/helpers/utilHelper';
import { preApprovedService } from '@/services/preApprovedService';
import BeenhereIcon from '@mui/icons-material/Beenhere';
import { closeSnackbar } from 'notistack';
import React, { useEffect, useState } from 'react';
import { checkPermisosAcciones } from '../(config)/checkPermisosAcciones';
import { ROLES_PARAMETROS } from '../(enums)/RolesParametrosEnum';
import { Box, Button, Divider, Typography } from '@mui/material';
import { REVALIDAR_INTERNO } from '../(enums)/FormPreAprobadosPgs';
import LoaderHelper from '@/helpers/loaderHelper';
import { TITULO_LOADER, CONTENIDO_LOADER } from '../(enums)/LoaderEnum';

const RevalidateMenu = ({ onFetchData }: { onFetchData: () => void }) => {
    const [loading, setLoading] = useState(false);
    const [accionesHabilitadas, setAccionesHabilitadas] = useState<{ [key: string]: boolean }>({});
    const [open, setOpen] = useState(false);
    const [faseRevalidacion, setFaseRevalidacion] = useState<JSX.Element | string>('');

    useEffect(() => {
        const obtenerPermisos = async () => {
            const permisos = await checkPermisosAcciones([ROLES_PARAMETROS.RECONSULTAR_EQUIFAX]);
            setAccionesHabilitadas(permisos);
        };
        obtenerPermisos();
    }, []);

    const revalidate = async () => {
        let ultimoPeriodo = utilHelper.calculatePeriodo();

        if (!utilHelper.esValido(ultimoPeriodo)) {
            return;
        }
        let textoMensaje = "¿Está seguro que desea revalidar?";

        notify.confirmation({
            message: textoMensaje,
            onConfirm: (snackbarId) => {
                closeSnackbar(snackbarId);
                setLoading(true);
                const confirmHandler = async () => {
                    try {
                        setOpen(true);
                        const params = {
                            revalidar: REVALIDAR_INTERNO.REVALIDAR_PARAM,
                            periodo: ultimoPeriodo
                        };
                        let resultado;
                        let contadorFases = 1;

                        do {
                            setFaseRevalidacion(
                                <>
                                    <Box sx={{ display: 'flex', width: '100%' }}>
                                        <Divider variant="fullWidth" sx={{ margin: 2, width: '95%', color: '#1D428A' }}>
                                            DETALLES DE LA EJECUCIÓN
                                        </Divider>
                                    </Box>
                                    <Box sx={{ display: 'flex', width: '100%' }}>
                                        <Typography align='center' variant='h6' fontWeight='bold' sx={{ marginLeft: 2, marginTop: 2, width: '100%' }}>
                                            {CONTENIDO_LOADER.FASE_REVALIDAR} {contadorFases++}
                                        </Typography>
                                    </Box>
                                </>
                            )
                            resultado = await preApprovedService.internalRevalidatePreApprovedClients(params);
                            if (resultado?.status !== 200) {
                                notify.error("Ocurrió un error durante el proceso de revalidación.");
                                break;
                            }
                        } while (resultado?.data === REVALIDAR_INTERNO.REVALIDAR_PENDIENTE);

                        if (resultado?.data === REVALIDAR_INTERNO.OK) {
                            notify.success("Revalidación exitosa.");
                            onFetchData();
                        } else if (utilHelper.esValido(resultado?.data)) {
                            notify.error(`Error al revalidar. \n ${resultado?.data ?? ""}`);
                        }
                    } catch (error) {
                        notify.error("Ocurrió un error durante el proceso de revalidación.");
                        console.error(error);
                    } finally {
                        setOpen(false);
                        setLoading(false);
                    }
                };
                confirmHandler();
            },
        });
    };

    return (
        <>
            {loading ? (
                <LoaderHelper tituloLoader={TITULO_LOADER.REVALIDANDO_CLIENTES} contenido={faseRevalidacion} mostrarLoader={open} />
            ) : null}
            <Button
                variant='contained'
                disabled={!accionesHabilitadas.reconsultarequifax}
                color='info'
                startIcon={<BeenhereIcon />}
                onClick={revalidate}
            >
                REVALIDAR INTERNO
            </Button>
        </>
    )
}

export default RevalidateMenu
