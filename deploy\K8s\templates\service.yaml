apiVersion: v1 # versión de la api de kubernetes que se utiliza
kind: Service # es el objeto de la API de Kubernetes que describe cómo se accede a las aplicaciones, tal como un conjunto de Pods, y que puede describir puertos y balanceadores de carga.
metadata: 
  name: {{ .Values.releaseName }} # nombre del servicio
  labels:
    app: {{ .Values.releaseName }}-app
    service: {{ .Values.releaseName }}
spec:
  type: ClusterIP # tipo de acceso al pod (ClusterIP, NodePort, loadBalancer o externalName) por defectto ClusterIP
  ports:
    - port: 80 # puerto por donde accederá el servicio
      protocol: TCP # Protocolo de comunicación
      name: http 
      targetPort: 3000
  selector: 
    service: {{ .Values.releaseName }}