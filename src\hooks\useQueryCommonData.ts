import { useUtilStore } from "@/stores/utilStore";
import { useEffect } from "react";
import { useQueryBranches } from "./queries/useQueryBranches";

const useQueryCommonData = () => {
    const { data: branchesData } = useQueryBranches();
    const setBranches = useUtilStore(state => state.setBranches);
    useEffect(() => {
        if (branchesData) {
            setBranches(branchesData);
        }
    }, [branchesData]);
}

export {
    useQueryCommonData
};
