import { ESearchServices } from "@/constants/utils";
import { TextFieldVariants } from "@mui/material/TextField";
import { MRT_ColumnDef } from "material-react-table";
import { OptionItem, OptionItemCreatable, OptionListGridItem, TValues } from "./_common";

export enum FormDataType {
    HEADER_TITLE = "header_title",
    SWITCH_FIELD = "boolean",
    MULTI_SELECT_SERVER_FIELD = "listServer<string>",
    MULTI_SELECT_FIELD = "listMultiple<string>",
    MULTI_SELECT_CREATABLE_FIELD = "listCreatable<string>",
    SELECT_FIELD = "listSelect<string>",
    TEXT_FIELD = "string",
    NUMBER_FIELD = "number",
    CURRENCY_FIELD = "currency",
    RANGE_NUMBER_VALUE = "range_number_value",
    RANGE_CURRENCY_VALUE = "range_currency_value",
    RADIO_FIELD_GROUP = "boolean_group",
    LIST_DATA_GRID = "list_data_grid",
    EMPTY = "empty",
};

export interface FormField<TData extends TValues> {
    id: string;
    label: string;
    name: keyof TData | string;
    dataType: FormDataType;
    xs?: number;
    infoField?: string;
    placeholder?: string;
    fullWidth?: boolean;
    variant?: TextFieldVariants;
    shouldUnregister?: boolean;
    prefixText?: string;
    subfixText?: string;
    options?: OptionItem[];
    optionsCreatable?: OptionItemCreatable[];
    endPoint?: string;
    columns?: MRT_ColumnDef<OptionListGridItem>[];
    dataGridOptions?: DataGridOptions;
    searchService?: ESearchServices;
    rangeOptions?: RangeFieldOptions[];
    inputFieldVariant?: "multiple" | "single";
    maxLength?: number;
};

export interface DataGridOptions {
    counterTextSingular: string;
    counterTextPlural: string;
    inputLabel: string;
};

export interface FormGroup<TData extends TValues> {
    id: string;
    name: string;
    label: string;
    subLabel?: string;
    fields: FormField<TData>[];
}


export interface FormLayout<TData extends TValues> {
    groups: FormGroup<TData>[];
}

export interface RangeFieldOptions {
    id: string;
    name: string;
    label: string;
    placeholder?: string;
    min: number;
    max: number;
    step: number;
    unit: string;
}

export interface RadioGroupFieldOptions {
    label: string;
    options: OptionItem[];
}

export interface FormGroubTab<T extends TValues = any> {
    id: string;
    label: string;
    formLayout: FormLayout<T>;
}