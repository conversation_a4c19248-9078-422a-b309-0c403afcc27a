import { baseApi } from "@/lib/api";
import { AuthUserModel } from "@/models/responses/authServices";
import { authServices } from "@/services/authServices";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    user?: AuthUserModel,
    error?: string
    status: number
}
export async function GET(req: NextRequest, res: NextResponse) {
    let responseBody: Response = {
        user: undefined,
        error: undefined,
        status: 200
    };
    const session = await getServerSession();

    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 404;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    baseApi.defaults.nextRequest = req;

    const userEmail = session?.user.email;
    if (userEmail) {
        const userName = userEmail.split("@")[0];
        const resp = await authServices.getAuthUser(userName);
        responseBody.user = resp.data;
    } else {
        responseBody.error = "No se encontró el usuario";
        responseBody.status = 404;
    }
    return NextResponse.json(responseBody.user, { status: responseBody.status })
}