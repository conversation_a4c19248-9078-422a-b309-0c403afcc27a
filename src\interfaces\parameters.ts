import { ParametersModel } from "@/models/responses/parameterService";
import { OptionItem, OptionItemCreatable, OptionListGridItem } from "./_common";

export interface PreApprovedParameters {
    idGrupo?: string;
    calificacionSisFinanciero?: string[];
    refAtraso?: string[];
    tipoCliente?: OptionItem;
    poseeCuentaGs?: string;
    esCodeudor?: string;
    conceptosAceptadosParaPromedio?: OptionItemCreatable[];
    coeficienteGastosAdministrativos?: number;
    seguroCoeficiente?: string;
    exclusionEmpresas?: OptionListGridItem[];
    exclusionSucursales?: OptionListGridItem[];
    ALDnegativo?: boolean;
    riesgoOperacionSospechosa?: boolean;
    montoLimite?: number;
    maximoEdadLimite?: number;
    minimoEdadLimite?: number;
    maximoEdadLimiteEssap?: number;
    minimoEdadLimiteEssap?: number;
    promedioAcreditacion?: number;
    promedioPeriodoAcreditacion?: number;
    antiguedad?: number;
    rangoInforcomf?: string;
    nivelEndeudamiento?: number;
    calificacionBcp?: string[];
    tieneMora?: string;
    cantidadPrestamos?: number;
    porcentajePagoCredito?: number;
    minimoMontoPrestableLimite?: number;
    maximoMontoPrestableLimite?: number;
    inhabilitadoInfocheck?: boolean;
    nivelEndeudamientoEmpresasGrupo?: number;
    montoMinimoPrestamosPreaprobados?: number;
}

export interface PreApprovedParametersGroup {
    id: string;
    parameters: PreApprovedParameters;
}

export interface PreApprovedFormParameters {
    grupos: PreApprovedParametersGroup[];
}

export interface ParametersMappingModel {
    [key: string]: ParametersModel[]
}

export interface ParameterMetadata {
    fieldName: string;
    groupId: string;
    fieldId: string;
    state: "pending" | "approved";
}