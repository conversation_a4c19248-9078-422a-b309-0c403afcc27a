import { AuthResponseModel, AuthUserModel, UserPermissionsResponseModel } from "@/models/responses/authServices";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

export interface AuthState {
    userName: string;
    setUserName: (userName: string) => void;
    authenticated: boolean;
    setAuthenticated: (authenticated: boolean) => void;
    auth?: AuthResponseModel;
    setAuth: (auth?: AuthResponseModel) => void;

    kcToken?: string;
    setKcToken: (kcToken?: string) => void;

    permissions?: UserPermissionsResponseModel[];
    setPermissions: (permissions?: UserPermissionsResponseModel[]) => void;

    user?: AuthUserModel;
    setUser: (user?: AuthUserModel) => void;
};

export const useAuthStore = create(persist<AuthState>(
    (set) => ({
        userName: "",
        setUserName: (userName: string) => set(
            (state: AuthState) => ({ ...state, userName })
        ),
        authenticated: false,
        setAuthenticated: (authenticated: boolean) => set(
            (state: AuthState) => ({ ...state, authenticated })
        ),
        auth: "",
        setAuth: (auth?: AuthResponseModel) => set(
            (state: AuthState) => ({ ...state, auth })
        ),
        kcToken: "",
        setKcToken: (kcToken?: string) => set(
            (state: AuthState) => ({ ...state, kcToken })
        ),
        permissions: [],
        setPermissions: (permissions?: UserPermissionsResponseModel[]) => set(
            (state: AuthState) => ({ ...state, permissions })
        ),
        user: undefined,
        setUser: (user?: AuthUserModel) => set(
            (state: AuthState) => ({ ...state, user })
        ),
    }),
    {
        name: "auth-storage",
        storage: createJSONStorage(() => sessionStorage),
        
    }
));

export const useUser = () => {
    const user = useAuthStore((state) => {
        return state.user;
    });
    return user;
}

export const useUserName = () => {
    const userName = useAuthStore((state) => {
        return state.userName;
    });
    return userName;
}

export const useApimToken = () => {
    const apimToken = useAuthStore(state => state.auth?.access_token);
    return apimToken;
}

export const getApimTokenOutside = () => {
    return useAuthStore.getState().auth?.access_token;
}

export const getApimTokenTypeOutside = () => {
    return useAuthStore.getState().auth?.token_type;
}

export const useAuthUserPermissions = () => {
    const permissions = useAuthStore(state => state.permissions);
    return permissions;
}