import { utilHelper } from '@/helpers/utilHelper';
import { RolesParametros } from '@/models/responses/usuariosServices';
import { usuarioServices } from '@/services/usuarioServices';
interface PermisosPorAccion {
    [key: string]: boolean;
}

export const checkPermisosAcciones = async (permisosARevisar: string[]): Promise<PermisosPorAccion> => {
    const authStorage = JSON.parse(sessionStorage.getItem('auth-storage') ?? '');
    const codigoUserActual = authStorage?.state?.user?.codigo;
    const permisos: PermisosPorAccion = {};

    if (utilHelper.esValido(codigoUserActual)) {
        const acciones = await usuarioServices.getAccionesInterno();
        const accionesUsuario = await usuarioServices.getAccionesUsuarioInterno(codigoUserActual);
    
        if (utilHelper.esValido(accionesUsuario.data) && utilHelper.esValido(acciones.data)) {
            const rolesUsuario = accionesUsuario.data;
            let rolesUserFormat: RolesParametros[] = [];

            rolesUsuario.forEach(ru => {
                return acciones.data.forEach((a: { idRol: number; descripcion: string; }) => {
                    if (ru.idRol === a.idRol && ru.estado == "A") {
                        rolesUserFormat.push({
                            idRol: a.idRol,
                            descripcion: a.descripcion,
                            estado: ru.estado
                        });
                    }
                })
            });

            permisosARevisar.forEach((permiso) => {
                const tienePermisos = rolesUserFormat.some((obj: any) =>
                    obj.descripcion.toUpperCase().trim() === permiso.toUpperCase()
                );
                const key = permiso.replace(/\s+/g, '').toLowerCase();
                permisos[key] = tienePermisos;
            });
        }
    }

    return permisos || {};
};