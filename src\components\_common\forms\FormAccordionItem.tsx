import { TValues } from '@/interfaces/_common';
import { FormGroup } from '@/interfaces/forms';
import { useAccordionActiveIndex, useFormAccordionStore } from '@/stores/formAccordionStore';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import { SyntheticEvent, memo, useTransition } from 'react';
import FormAccordionHeader from './FormAccordionSummary';
import FormRenderFactory from './factory/FormRenderFactory';

interface AccordionItemProps<TData extends TValues> {
    index: number;
    itemGroup: FormGroup<TData>
}

const FormAccordionItem = memo(function FormAccordionItem<TData extends TValues>({ itemGroup, index }: AccordionItemProps<TData>) {
    const { label, subLabel } = itemGroup;
    const activeIndex = useAccordionActiveIndex(index);
    const [isPending, startTransition] = useTransition();

    const toggleAccordion = useFormAccordionStore((state) => state.toggleAccordion);

    const handleChange = (event: SyntheticEvent<Element, Event>, expanded: boolean) => {
        if (expanded) {
            startTransition(() => {
                toggleAccordion(index, expanded)
            });
        } else {
            toggleAccordion(index, expanded)
        }
    };

    const expanded = activeIndex;

    return (
        <Accordion
            expanded={expanded}
            onChange={handleChange}
            TransitionProps={{ unmountOnExit: true }}
        >
            <FormAccordionHeader index={index} label={label} subLabel={subLabel} itemGroup={itemGroup} />
            <AccordionDetails>
                {!isPending && <FormRenderFactory<TData> fields={itemGroup.fields} groupId={itemGroup.id} />}

            </AccordionDetails>
        </Accordion>
    )
}) as <TData extends TValues>(props: AccordionItemProps<TData>) => JSX.Element;

export default FormAccordionItem