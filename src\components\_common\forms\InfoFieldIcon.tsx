import InfoIcon from '@mui/icons-material/Info';
import { SxProps, Theme } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import React from 'react';

interface InfoIconProps {
    onClick: (event: React.MouseEvent<HTMLElement>) => void;
    sx?: SxProps<Theme>
}
const InfoFieldIcon = ({ onClick, sx }: InfoIconProps) => {
    return (
        <IconButton size="small" sx={{ padding: 0, ...sx }} onClick={onClick}><InfoIcon /></IconButton>
    )
}

export default InfoFieldIcon