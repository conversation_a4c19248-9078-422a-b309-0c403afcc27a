import { TValues } from "@/interfaces/_common";
import { ColumnFiltersState } from "@tanstack/react-table";

const filterData = <TData extends TValues>(data: TData[], columnFilters: ColumnFiltersState) => {
    const filteredData = data.filter((item) => {
        let result = true;
        columnFilters.forEach(({ id: key, value }) => {
            const itemValue = item[key];
            
            result = false;
            if (
                itemValue !== undefined
                && typeof itemValue === 'string'
                && typeof value === 'string'
            ) {
                if (itemValue.trim().toLowerCase().includes(value.trim().toLowerCase())) {
                    result = true;
                }
            } else if (
                itemValue !== undefined
                && typeof itemValue === 'number'
                && typeof value === 'number'
            ) {
                
                if (itemValue === value) {
                    result = true;
                }
            } else if (itemValue !== undefined
                && Array.isArray(value)
                && value.length == 2
                && typeof value[0] === 'number'
                && typeof value[1] === 'number'
            ) {
                const [min, max] = value;
                if (itemValue >= min && itemValue <= max) {
                    result = true;
                }
            } else if (itemValue !== undefined
                && typeof value === 'boolean'
            ) {
                if (itemValue === value) {
                    result = true;
                }
            } else if (itemValue !== undefined
                && Array.isArray(value)
                && value.length > 0
                && typeof value[0] === 'string'
            ) {
                if (value.includes(itemValue)) {
                    result = true;
                }
            }

            
        });

        return result;
    });

    return filteredData;
};

const prefetchDataPaginated = <TData extends TValues>(
    prefetchData: TData[],
    pageIndex: number,
    pageSize: number,
    columnFilters: ColumnFiltersState,
) => {
    const filteredData = filterData(prefetchData, columnFilters);
    const total = filteredData.length;
    const totalPages = Math.ceil(total / pageSize);
    const startIndex = pageIndex * pageSize;
    const page = filteredData.slice(startIndex, startIndex + pageSize);
    return {
        data: page,
        total,
        totalPages
    }
}

const getMinMaxValuesByColumnId = (data: TValues[], columnId: string) => {
    const values = data.map((item) => {
        const value = item[columnId];
        const value_ = typeof value === 'string' ? parseFloat(value) : value;
        if (typeof value_ === 'number') {
            return value;
        }
        return 0;
    });
    const min = Math.min(...values);
    const max = Math.max(...values);
    return {
        min,
        max
    }
}

const getUniqueValuesByColumnId = (data: TValues[], columnId: string) => {
    const values = data.map((item) => {
        const value = item[columnId];
        return value;
    });

    const uniqueValueMap = new Map<string, number>();
    values.forEach((value) => {
        if (uniqueValueMap.has(value)) {
            const count = uniqueValueMap.get(value) ?? 0;
            uniqueValueMap.set(value, count + 1);
        } else {
            uniqueValueMap.set(value, 1);
        }
    });
    return uniqueValueMap;
}


export const dataTableHelpers = {
    prefetchDataPaginated,
    getMinMaxValuesByColumnId,
    getUniqueValuesByColumnId
}