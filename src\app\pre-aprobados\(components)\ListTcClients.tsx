"use client"
import ExportButton from '@/components/_common/Buttons/ExportButton';
import { EListViewEndpoint } from '@/constants/listView';
import exportHelper from '@/helpers/exportHelper';
import notify from '@/helpers/notifyHelper';
import { useQueryPreApprovedTc } from '@/hooks/parameters/useQueryPreApprovedTc';
import { TCClientsModel } from '@/models/responses/clientServices';
import { useFilterListParameters } from '@/stores/filterListParameters';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import FilterAltOffIcon from '@mui/icons-material/FilterAltOff';
import { IconButton } from '@mui/material';
import Box from '@mui/material/Box';
import { MRT_ColumnDef, MRT_RowSelectionState } from 'material-react-table';
import { useMemo, useState } from 'react';
import { exportConfigTcClients } from '../(config)/exportConfig';
import { closeSnackbar } from 'notistack';

const ListTcClients = () => {
    const columns: MRT_ColumnDef<TCClientsModel>[] = useMemo<MRT_ColumnDef<TCClientsModel>[]>(
        () => [
            {
                accessorKey: "tipoCuenta",
                header: "Tipo Cuenta",
                size: 50,
            },
            {
                accessorKey: "idAfinidad",
                header: "Id Afinidad",
                size: 50
            },
            {
                accessorKey: "departamento",
                header: "Departamento",
                size: 50
            },
            {
                accessorKey: "ciudad",
                header: "Ciudad",
                size: 50
            },
            {
                accessorKey: "oficial",
                header: "Oficial",
                size: 50
            },
            {
                accessorKey: "cuenta",
                header: "Cuenta",
                size: 50
            },
            {
                accessorKey: "cuentaVip",
                header: "Cuenta Vip",
                size: 50
            },
            {
                accessorKey: "cuentaFranqueo",
                header: "Cuenta Franqueo",
                size: 50
            },
            {
                accessorKey: "seguroVida",
                header: "Seguro Vida",
                size: 50
            },
            {
                accessorKey: "cobroCargos",
                header: "Cobro Cargos",
                size: 50
            },
            {
                accessorKey: "tipoCosto",
                header: "Tipo Costo",
                size: 50
            },
            {
                accessorKey: "retenerExtracto",
                header: "Retener Extracto",
                size: 50
            },
            {
                accessorKey: "codigoCliente",
                header: "Codigo Cliente",
                size: 50
            },
            {
                accessorKey: "linea",
                header: "Linea",
                size: 50
            },
            {
                accessorKey: "lineaCredito",
                header: "Linea Credito",
                size: 50
            },
            {
                accessorKey: "cuota",
                header: "Cuota",
                size: 50
            },
            {
                accessorKey: "tipoPago",
                header: "Tipo Pago",
                size: 50
            },
            {
                accessorKey: "valorCFG",
                header: "Valor CFG",
                size: 50
            },
            {
                accessorKey: "tasaFinanciacion",
                header: "Tasa Financiacion",
                size: 50
            },
            {
                accessorKey: "factorVip",
                header: "Factor Vip",
                size: 50
            },
            {
                accessorKey: "motivoRetencion",
                header: "Motivo Retencion",
                size: 50
            },
        ],
        [],
    );
    const initialRowSelection = useMemo(() => ({}), []); // Estado inicial memoizado
    const [rowSelection, setRowSelection] = useState<MRT_RowSelectionState>(initialRowSelection);

    const sideFilterOpened = useFilterListParameters((state => state.sideFilterOpened));
    const setSideFilterOpened = useFilterListParameters((state => state.toggleShowSideFilter));

    const { data: data_, isLoading, isRefetching } = useQueryPreApprovedTc({});

    const handleClickFilter = () => {
        setSideFilterOpened();
    };

    const handleClickExport = () => {
        if (data_?.data?.length === 0) return;
        notify.confirmation({
            message: "¿Desea exportar los datos?",
            onConfirm: () => {
                exportHelper.exportToXsl({
                    data: data_?.data ?? [],
                    fileName: "pre-aprobados",
                    mapperFunction: exportConfigTcClients
                });
                closeSnackbar();
                notify.success("Datos exportados correctamente");
            }
        })

    };

    const loading = isLoading || isRefetching;
    return (
        <>
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    marginTop: "25px",
                    marginBottom: 1,
                }}
            >
                <IconButton
                    onClick={handleClickFilter}
                    sx={{
                        backgroundColor: "#f5f5f5",
                    }}
                >
                    {sideFilterOpened ? <FilterAltOffIcon /> : <FilterAltIcon />}
                </IconButton>
                <Box sx={{
                    display: "flex",
                    gap: 1
                }}>
                    <ExportButton onClick={handleClickExport} fullWidth>Exportar</ExportButton>
                </Box>

            </Box>
            <Box
                sx={{
                    display: "flex",
                }}

            >
            </Box>
        </>

    )
}

export default ListTcClients