import { HeaderExportMapper, TValues } from '@/interfaces/_common';
import exportFromJSON from 'export-from-json';

interface ExportParams<TData extends TValues> {
    data: TData[],
    fileName: string;
    mapperFunction?: HeaderExportMapper<TData>[];
}

const exportToXsl = <TData extends TValues = any>({ data, fileName, mapperFunction }: ExportParams<TData>) => {
    const exportType = exportFromJSON.types.xls
    const dataToExport = mapperFunction ? data.map((row) => {
        const rowToExport: TValues = {};
        mapperFunction.forEach((header) => {
            rowToExport[header.header.toUpperCase()] = header.accessorFn(row);
        });
        return rowToExport;
    }) : data;

    exportFromJSON({
        data: dataToExport,
        fileName,
        exportType
    })
};

const exportHelper = {
    exportToXsl
};

export default exportHelper;