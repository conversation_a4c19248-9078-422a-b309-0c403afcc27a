import { useWindowSize } from '@/hooks/useWindowsSize';
import { TValues } from '@/interfaces/_common';
import { useFilterListParameters } from '@/stores/filterListParameters';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper';
import { MRT_PaginationState, MRT_TableInstance } from 'material-react-table';
import React, { useMemo, useRef, useState } from 'react';

interface CustomFilterContainerProps<TData extends TValues> {
    children: React.ReactNode
    table: MRT_TableInstance<TData>
}
const CustomFilterContainer = <TData extends TValues>({ children, table }: CustomFilterContainerProps<TData>) => {

    const [pagination, setPagination] = useState<MRT_PaginationState>({
        pageIndex: 1,
        pageSize: 100000,
    });

    const conatinerRef = useRef<HTMLDivElement>(null);
    const { getRestantHeightPx } = useWindowSize(conatinerRef);
    const sideFilterOpened = useFilterListParameters((state => state.sideFilterOpened));
    return (
        <Paper
            ref={conatinerRef}
            sx={{
                width: sideFilterOpened ? "300px" : 0,
                height: `calc(${getRestantHeightPx(0, 6)})`,
            }}
        >
            <Box
                sx={{
                    overflowY: "auto",
                    height: "calc(100% - 45px)",
                    padding: 2,

                }}
            >
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "center",
                        gap: 2,
                    }}
                >
                    {children}
                </Box>
            </Box>

            <Box
                sx={{
                    paddingLeft: 2,
                    paddingRight: 2,
                }}
            >
                <Button
                    onClick={() => {
                        table.resetColumnFilters();
                        table.getCoreRowModel();
                    }}
                    variant="contained"
                    sx={{
                        width: "100%",
                    }}
                >
                    Limpiar
                </Button>
            </Box>
        </Paper>
    )
}

export default CustomFilterContainer