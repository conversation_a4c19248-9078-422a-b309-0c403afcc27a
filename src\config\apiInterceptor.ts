import { AUTH_LOGIN_ROUTE } from "@/constants/auth";
import { utilHelper } from "@/helpers/utilHelper";
import { getApimTokenOutside, getApimTokenTypeOutside } from "@/stores/authStore";
import { AxiosError, InternalAxiosRequestConfig } from "axios";

export const baseApiInterceptor = async (config: InternalAxiosRequestConfig) => {
    const isClientSide = utilHelper.isClientSide();

    if (isClientSide) {
        const token = getApimTokenOutside();
        const tokenType = getApimTokenTypeOutside();
        if (token) {
            config.headers.Authorization = `${tokenType} ${token}`;
        }
    } else if (config.nextRequest) {
        const authorization = config.nextRequest.headers.get("authorization") ?? "";
        if (authorization) {
            const token = authorization.replace("Bearer ", "");
            config.headers.Authorization = `Bearer ${token}`;
        }
    }
    const subscriptionKey = process.env.NEXT_PUBLIC_SUBSCRIPTION_KEY;

    if (subscriptionKey) {
        config.headers["Subscription-Key"] = subscriptionKey;
    }

    return config;
}

export const internalApiInterceptor = async (config: InternalAxiosRequestConfig) => {
    const isClientSide = utilHelper.isClientSide();
    if (isClientSide) {
        const token = getApimTokenOutside();
        const tokenType = getApimTokenTypeOutside();
        if (token) {
            config.headers.Authorization = `${tokenType} ${token}`;
        }
    }
    return config;
}

export const internalApiErrorResponseInterceptor = (error: AxiosError) => {
    if (error.response) {
        if (error.response.status === 401) {
            const currentUrl = window.location.href;
            const searchParams = new URLSearchParams();
            searchParams.append("callbackUrl", currentUrl);
            window.location.href = `${AUTH_LOGIN_ROUTE}?${searchParams.toString()}`;
        }
    }
    return Promise.reject(error);
}