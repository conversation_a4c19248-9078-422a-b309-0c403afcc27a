import { parametersFilters } from '@/app/pre-aprobados/(config)/filterListParametersConfig'
import { useWindowSize } from '@/hooks/useWindowsSize'
import { useFilterListParameters } from '@/stores/filterListParameters'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Paper from '@mui/material/Paper'
import { useRef } from 'react'
import FilterControls from './FilterControls/FilterControls'

const SideFilterContainer = () => {
    const sideFilterOpened = useFilterListParameters((state => state.sideFilterOpened));

    const conatinerRef = useRef<HTMLDivElement>(null);
    const { getRestantHeightPx } = useWindowSize(conatinerRef);

    const handleChangeFilter = () => {

    }

    return (
        <Paper
            ref={conatinerRef}
            sx={{
                display: "flex",
                flexDirection: "column",
                width: sideFilterOpened ? "400px" : 0,
                padding: sideFilterOpened ? 2 : 0,
                justifyContent: "space-between",
                height: `calc(${getRestantHeightPx(33)} - 32px)`,
                minWidth: 0,
                visibility: sideFilterOpened ? "visible" : "hidden",
                transition: "width 0.1s ease-in-out",
            }}
        >
            <FilterControls
                filters={parametersFilters}
                onChange={handleChangeFilter}
            />
            <Box sx={{
                display: "flex",
                alignSelf: "flex-end",
                justifyContent: "space-between",
                gap: 2,
                width: "100%",
            }}>
                <Button sx={{ flex: 1 }} variant='contained' color='inherit'>Limpiar</Button>
                <Button sx={{ flex: 1 }} variant='contained'>Aplicar</Button>
            </Box>
        </Paper>
    )
}

export default SideFilterContainer