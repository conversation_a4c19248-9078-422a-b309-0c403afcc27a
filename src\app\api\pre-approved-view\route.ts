import { PreApprovedClientsModel } from "@/models/responses/clientServices";
import { NextRequest, NextResponse } from "next/server"

const getData = async () => {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_API_URL;
    const resp = await fetch(`${baseUrl}/preApprovedClients`);
    const data = await resp.json() as PreApprovedClientsModel[];
    return data;
}

export async function GET(req: NextRequest, res: NextResponse) {
    const searchParams = req.nextUrl.searchParams
    const pageIndex = searchParams.get('pageIndex') ?? '0';
    const pageSize = searchParams.get('pageSize') ?? '10';
    const data = await getData()
    const total = data.length;
    const totalPages = Math.ceil(total / parseInt(pageSize));

    const startIndex = parseInt(pageIndex) * parseInt(pageSize);

    const page = data.slice(startIndex, startIndex + parseInt(pageSize));

    const responseBody = {
        data: page,
        total,
        totalPages
    };
    return NextResponse.json(responseBody, { status: 200 })
}