import { baseApi } from "@/lib/api";
import { UserPermissionsResponseModel } from "@/models/responses/authServices";
import { authServices } from "@/services/authServices";
import { isAxiosError } from "axios";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

interface Response {
    permissions?: UserPermissionsResponseModel[],
    error?: string
    status: number
    meta?: any
}
export async function GET(req: NextRequest, res: NextResponse) {
    let responseBody: Response = {
        permissions: [],
        error: undefined,
        status: 200
    };
    const session = await getServerSession();
    if (!session) {
        responseBody.error = "No tiene autorización para acceder a este recurso";
        responseBody.status = 404;
        return NextResponse.json(responseBody, { status: responseBody.status })
    }
    baseApi.defaults.nextRequest = req;
    const url = new URL(req.url ?? "");
    const searchParams = url.searchParams;
    const userCode = searchParams.get("userCode");

    try {
        if (userCode) {
            const resp = await authServices.getAuthUserPermissions(userCode);
            responseBody.permissions = resp.data;
        } else {
            responseBody.error = "No se encontró el usuario";
            responseBody.status = 404;
        }

        return NextResponse.json(responseBody.permissions, { status: responseBody.status })
    } catch (error: any) {
        console.error(error);
        responseBody.error = "Error al obtener los permisos del cliente";
        responseBody.status = 500;
        responseBody.meta = error;
        if (isAxiosError(error)) {
            responseBody.error = error.message;
            responseBody.status = error?.status ?? 500;
            responseBody.meta = {
                data: error.config?.data,
                url: error.config?.url,
            };
        }
        return NextResponse.json(responseBody, { status: responseBody.status });
    }
}