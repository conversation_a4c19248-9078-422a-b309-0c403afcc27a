import { AuthOptions, Session } from "next-auth";
import KeycloakProvider from "next-auth/providers/keycloak";

export const authOptions: AuthOptions = {
    providers: [
        KeycloakProvider({
            clientId: process.env.NEXT_PUBLIC_KEYCLOAK_ID ?? "",
            clientSecret: process.env.NEXT_PUBLIC_KEYCLOAK_SECRET ?? "",
            issuer: process.env.NEXT_PUBLIC_KEYCLOAK_ISSUER,
        })
    ],

    secret: process.env.NEXTAUTH_SECRET,
    pages: {
        signIn: "/auth/signin",
        signOut: "/auth/signout",
        error: "/",
    },
    debug: true,
    callbacks: {
        jwt: async ({ token, user, account, profile }) => {
            if (account?.access_token) {
                token.accessToken = account?.access_token;
                token.email = profile?.preferred_username;
                token.idToken = account?.id_token;
            }
            return token;
        },
        signIn: async ({ user, account }) => {
            return true;
        },
        session: async ({ session, token }) => {
            const newSessionData = {
                ...session, 
                user: {
                    ...session.user,
                    email: token.email,
                }
            } as Session;
            return newSessionData;
        },
    },
    session: {
        maxAge: 25000,
        updateAge: 25000,
    },
}