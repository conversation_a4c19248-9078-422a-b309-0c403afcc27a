import notify from "@/helpers/notifyHelper";
import { utilHelper } from "@/helpers/utilHelper";
import { OptionItemCreatable, OptionListGridItem } from "@/interfaces/_common";
import { ParameterMetadata, ParametersMappingModel, PreApprovedFormParameters, PreApprovedParameters, PreApprovedParametersGroup } from "@/interfaces/parameters";
import { FormLinkWithId } from "@/interfaces/utils";
import { ParametersModel } from "@/models/responses/parameterService";
import { useFormStore } from "@/stores/formStore";
import { getBranchesOutside, getClientsOutside } from "@/stores/utilStore";

export const configParametersMapping: FormLinkWithId<ParametersModel, PreApprovedParameters>[] = [
    {
        id: "1",
        name: "nivelEndeudamiento",
        parameterName: "Porcentaje de Endeudamiento.",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") return Number(value.valorDesdePendiente);
            return Number(value.valorDesde);
        },
        toEntryValue: (value) => value.nivelEndeudamiento
    },
    {
        id: "2",
        name: "promedioAcreditacion",
        parameterName: "Monto cobro mínimo mensual.",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") return Number(value.valorDesdePendiente);
            return Number(value.valorDesde)
        },
        toEntryValue: (value) => value.promedioAcreditacion
    },
    {
        id: "3",
        name: "cantidadPrestamos",
        parameterName: "Cantidad de Préstamos",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") return Number(value.valorDesdePendiente);
            return Number(value.valorDesde)
        },
        toEntryValue: (value) => value.cantidadPrestamos
    },
    {
        id: "4",
        name: "refAtraso",
        parameterName: "Referencia de Atraso",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") {
                return value.valorDesdePendiente
                    ?.split(",")
                    ?.filter((item: string) => item !== "") ?? [];
            }

            return value.valorDesde
                ?.split(",")
                ?.filter((item: string) => item !== "") ?? [];
        },
        toEntryValue: (value) => value.refAtraso?.join(",")
    },
    {
        id: "5",
        name: "tipoCliente",
        parameterName: "Cliente Físico",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") {
                return {
                    label: value.valorDesdePendiente === "F" ? "Físico" : "Jurídico",
                    value: value.valorDesdePendiente
                }
            }

            return {
                label: value.valorDesde === "F" ? "Físico" : "Jurídico",
                value: value.valorDesde
            };
        },
        toEntryValue: (value) => value.tipoCliente?.value
    },
    {
        id: "6",
        name: "poseeCuentaGs",
        parameterName: "Posee cuenta en Guaraníes?",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") {
                return value.valorDesdePendiente == "SI" ? "true" : "false";
            }
            return value.valorDesde == "SI" ? "true" : "false";
        },
        toEntryValue: (value) => value.poseeCuentaGs == "true" ? "SI" : "NO"
    },
    {
        id: "7",
        name: "minimoEdadLimite",
        parameterName: "Rango de Edad Gral.",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") {
                if (value.valorDesdePendiente == null || value.valorDesdePendiente == "") {
                    return Number(value.valorDesde)
                } else {
                    return Number(value.valorDesdePendiente)
                }
            }
            return Number(value.valorDesde)
        },
        toEntryValue: (value) => value.minimoEdadLimite
    },
    {
        id: "7",
        name: "maximoEdadLimite",
        parameterName: "Rango de Edad Gral.",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") {
                if (value.valorHastaPendiente == null || value.valorHastaPendiente == "") {
                    return Number(value.valorHasta)
                } else {
                    return Number(value.valorHastaPendiente)
                }
            }

            return Number(value.valorHasta)
        },
        toEntryValue: (value) => value.maximoEdadLimite
    },
    {
        id: "8",
        name: "calificacionSisFinanciero",
        parameterName: "Calificación del Banco",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") {
                return value.valorDesdePendiente
                    ?.split(",")
                    ?.filter((item: string) => item !== "") ?? [];
            }

            return value.valorDesde
                ?.split(",")
                ?.filter((item: string) => item !== "") ?? [];
        },
        toEntryValue: (value) => value.calificacionSisFinanciero?.join(",")
    },
    {
        id: "9",
        name: "calificacionBcp",
        parameterName: "Calificación del BCP",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") {
                return value.valorDesdePendiente
                    ?.split(",")
                    ?.filter((item: string) => item !== "") ?? [];
            }

            return value.valorDesde
                ?.split(",")
                ?.filter((item: string) => item !== "") ?? [];
        },
        toEntryValue: (value) => value.calificacionBcp?.join(",")
    },
    {
        id: "10",
        name: "esCodeudor",
        parameterName: "Es Codeudor?",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") {
                return value.valorDesdePendiente == "SI" ? "true" : "false";
            }

            return value.valorDesde == "SI" ? "true" : "false";
        },
        toEntryValue: (value) => value.esCodeudor == "true" ? "SI" : "NO"
    },
    {
        id: "11",
        name: "antiguedad",
        parameterName: "Antiguedad Laboral",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") {
                return Number(value.valorDesdePendiente);
            }

            return value.valorDesde
        },
        toEntryValue: (value) => value.antiguedad
    },
    {
        id: "12",
        name: "tieneMora",
        parameterName: "Mora",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") {
                return value.valorDesdePendiente == "SI" ? "true" : "false"
            }
            return value.valorDesde == "SI" ? "true" : "false"
        },
        toEntryValue: (value) => value.tieneMora == "true" ? "SI" : "NO"
    },
    {
        id: "13",
        name: "promedioPeriodoAcreditacion",
        parameterName: "Meses para calculo de Promedio.",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") return Number(value.valorDesdePendiente)
            return Number(value.valorDesde)
        },
        toEntryValue: (value) => value.promedioPeriodoAcreditacion
    },
    {
        id: "14",
        name: "conceptosAceptadosParaPromedio",
        parameterName: "Conceptos a tener en cuenta para el promedio.",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") {
                return value.valorDesdePendiente
                    ?.split(",")
                    ?.filter((item: string) => item !== "")
                    ?.map((item: string) => {
                        return {
                            value: item,
                            inputValue: "",
                            isNew: false,
                        } as OptionItemCreatable;
                    }) ?? [];
            }


            return value.valorDesde
                ?.split(",")
                ?.filter((item: string) => item !== "")
                ?.map((item: string) => {
                    return {
                        value: item,
                        inputValue: "",
                        isNew: false,
                    } as OptionItemCreatable;
                }) ?? [];
        },
        toEntryValue: (value) => value.conceptosAceptadosParaPromedio?.map(item => {
            return item.value
        }).join(","),
        isModified: (value, oldValue) => {
            const differentLength = value.conceptosAceptadosParaPromedio?.length != oldValue.conceptosAceptadosParaPromedio?.length;
            const differentData = value.conceptosAceptadosParaPromedio?.some((item, index) => {
                return item.value != oldValue.conceptosAceptadosParaPromedio?.[index]?.value
            });
            const differentNewData = value.conceptosAceptadosParaPromedio?.some((item, index) => {
                return item.isNew == true
            })
            return (differentLength || differentData || differentNewData) ?? false
        }
    },
    {
        id: "15",
        name: "coeficienteGastosAdministrativos",
        parameterName: "Coeficiente Gastos Administrativos",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") return Number(value.valorDesdePendiente);
            return Number(value.valorDesde)
        },
        toEntryValue: (value) => value.coeficienteGastosAdministrativos
    },
    {
        id: "16",
        name: "seguroCoeficiente",
        parameterName: "Coeficiente Seguro",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") return value.valorDesdePendiente
            return value.valorDesde
        },
        toEntryValue: (value) => value.seguroCoeficiente
    },
    {
        id: "17",
        name: "ALDnegativo",
        parameterName: "Listas Negativas.",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") {
                return value.valorDesdePendiente
                    ?.split(",")
                    .map((item: string) => {
                        return item.split("=")[1].trim();
                    })
                    ?.includes("NO") ? false : true;
            }

            return value.valorDesde
                ?.split(",")
                .map((item: string) => {
                    return item.split("=")[1].trim();
                })?.includes("NO") ? false : true;
        },
        toEntryValue: (value) => value.ALDnegativo ? "OFAC = SI ,ALD = SI ,ONU = SI" : "OFAC = NO ,ALD = NO ,ONU = NO"
    },
    {
        id: "18",
        name: "exclusionSucursales",
        parameterName: "Sucursales Excluidas",
        toOutputValue: (value: ParametersModel) => {
            const currentBranches = getBranchesOutside();

            if (value.estado == "PENDIENTE") {
                const branchesOptions: OptionListGridItem[] = value.valorDesdePendiente
                    ?.split(",")
                    ?.filter((item: string) => item !== "")
                    ?.map((item: string) => {
                        return {
                            label: currentBranches.find((branch) => branch.codigo == item)?.descripcion ?? "",
                            value: item,
                        };
                    }) ?? [];
                return branchesOptions;
            }

            const branchesOptions: OptionListGridItem[] = value.valorDesde?.split(",")
                ?.filter((item: string) => item !== "")
                ?.map((item: string) => {
                    return {
                        label: currentBranches.find((branch) => branch.codigo == item)?.descripcion ?? "-",
                        value: item,
                    };
                }) ?? [];
            return branchesOptions;
        },
        toEntryValue: (value) => value.exclusionSucursales
            ?.filter(i => !i.isDeleted)
            ?.map(item => {
                return `${item.value}`
            }).join(","),
        isModified: (value, oldValue) => {
            const differentLength = value.exclusionSucursales?.length != oldValue.exclusionSucursales?.length;
            const differentData = value.exclusionSucursales?.some((item, index) => {
                return item.value != oldValue.exclusionSucursales?.[index]?.value
            });

            const deletedData = value.exclusionSucursales?.some((item, index) => {
                return item.isDeleted == true
            })

            return (differentLength || differentData || deletedData) ?? false
        }
    },
    {
        id: "20",
        name: "riesgoOperacionSospechosa",
        parameterName: "ROS",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") return value.valorDesdePendiente == "NO" ? false : true
            return value.valorDesde == "NO" ? false : true
        },
        toEntryValue: (value) => value.riesgoOperacionSospechosa == false ? "NO" : "SI"
    },
    {
        id: "23",
        name: "rangoInforcomf",
        parameterName: "Faja Máxima Equifax",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") return value.valorDesdePendiente
            return value.valorDesde
        },
        toEntryValue: (value) => value.rangoInforcomf
    },

    {
        id: "25",
        name: "porcentajePagoCredito",
        parameterName: "Amortizacion del credito actual",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") return value.valorDesdePendiente
            return value.valorDesde
        },
        toEntryValue: (value) => value.porcentajePagoCredito
    },
    {
        id: "24",
        name: "minimoMontoPrestableLimite",
        parameterName: "Montos Minimos y Maximos",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") return Number(value.valorDesdePendiente)
            return Number(value.valorDesde)
        },
        toEntryValue: (value) => value.minimoMontoPrestableLimite
    },
    {
        id: "24",
        name: "maximoMontoPrestableLimite",
        parameterName: "Montos Minimos y Maximos",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") return Number(value.valorHastaPendiente)
            return Number(value.valorHasta)
        },
        toEntryValue: (value) => value.maximoMontoPrestableLimite
    },
    {
        id: "25",
        name: "nivelEndeudamientoEmpresasGrupo",
        parameterName: "Nivel de Endeudamiento Empresas del Grupo",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") return Number(value.valorDesdePendiente)
            return Number(value.valorDesde)
        },
        toEntryValue: (value) => value.nivelEndeudamientoEmpresasGrupo
    },
    {
        id: "26",
        name: "montoMinimoPrestamosPreaprobados",
        parameterName: "Monto mínimo para prestamos preaprobados",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") return Number(value.valorDesdePendiente)
            return Number(value.valorDesde)
        },
        toEntryValue: (value) => value.montoMinimoPrestamosPreaprobados
    },
    {
        id: "27",
        name: "inhabilitadoInfocheck",
        parameterName: "InhabilitadoInfocheck",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") return value.valorDesdePendiente === "1";
            return value.valorDesde === "1";
        },
        toEntryValue: (value) => value.inhabilitadoInfocheck == true ? 1 : 0
    },
    {
        id: "28",
        name: "maximoEdadLimiteEssap",
        parameterName: "Rango Edad ESSAP.",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") {
                if (value.valorHastaPendiente == null || value.valorHastaPendiente == "") {
                    return Number(value.valorHasta)
                } else {
                    return Number(value.valorHastaPendiente)
                }
            }
            return Number(value.valorHasta)
        },
        toEntryValue: (value) => value.maximoEdadLimiteEssap
    },
    {
        id: "28",
        name: "minimoEdadLimiteEssap",
        parameterName: "Rango Edad ESSAP.",
        toOutputValue: (value: ParametersModel) => {
            if (value.estado == "PENDIENTE") {
                if (value.valorDesdePendiente == null || value.valorDesdePendiente == "") {
                    return Number(value.valorDesde)
                } else {
                    return Number(value.valorDesdePendiente)
                }
            }

            return Number(value.valorDesde)
        },
        toEntryValue: (value) => value.minimoEdadLimiteEssap
    },
    {
        id: "29",
        name: "exclusionEmpresas",
        parameterName: "Empresas Excluidas",
        toOutputValue: (value: ParametersModel) => {
            const currentClient = getClientsOutside();

            if (value.estado == "PENDIENTE") {
                const clientsOptions: OptionListGridItem[] = value.valorDesdePendiente
                    ?.split(",")
                    ?.filter((item: string) => item !== "")
                    ?.map((item: string) => {
                        const client = currentClient.find((client) => client.codigoCliente == item);
                        if (client) {
                            const { primerNombre, segundoNombre, primerApellido, segundoApellido } = client;
                            const nombreCompleto = `${primerNombre} ${segundoNombre} ${primerApellido} ${segundoApellido}`;
                            return {
                                label: nombreCompleto,
                                value: item,
                            };
                        } else {
                            return {
                                label: "",
                                value: item,
                            };
                        }

                    }) ?? [];
                return clientsOptions;
            }

            const clientsOptions: OptionListGridItem[] = value.valorDesde
                ?.split(",")
                ?.filter((item: string) => item !== "")
                ?.map((item: string) => {
                    const client = currentClient.find((client) => client.codigoCliente == item);

                    if (client) {
                        const { primerNombre, segundoNombre, primerApellido, segundoApellido } = client;
                        const nombreCompleto = `${primerNombre} ${segundoNombre} ${primerApellido} ${segundoApellido}`;
                        return {
                            label: nombreCompleto,
                            value: item,
                        };
                    } else {
                        return {
                            label: "",
                            value: item,
                        };
                    }

                }) ?? [];
            
            return clientsOptions;
        },
        toEntryValue: (value) => value.exclusionEmpresas
            ?.filter(i => !i.isDeleted)
            ?.map(item => {
                return `${item.value}`
            }).join(","),
        isModified: (value, oldValue) => {
            const differentLength = value.exclusionEmpresas?.length != oldValue.exclusionEmpresas?.length;
            const differentData = value.exclusionEmpresas?.some((item, index) => {
                return item.value != oldValue.exclusionEmpresas?.[index]?.value
            });

            const deletedData = value.exclusionEmpresas?.some((item, index) => {
                return item.isDeleted == true
            })

            return (differentLength || differentData || deletedData) ?? false
        }
    },
]

const mappingEntryParameters = (data: ParametersModel[]): PreApprovedFormParameters => {
    const groupedData: ParametersMappingModel = data.reduce<ParametersMappingModel>((acc, obj) => {
        const idGrupo = obj.idGrupo;
        const parameterModel = { ...obj as ParametersModel };
        if (Object.keys(acc).includes(idGrupo) == false) {
            acc[idGrupo] = [{ ...obj as ParametersModel }];
        } else {
            acc[idGrupo].push(parameterModel as ParametersModel);
        }
        return acc;
    }, {});
    const grupos = Object.keys(groupedData);
    const preApprovedParametersGroup: PreApprovedParametersGroup[] = [];
    const parametersMetadata: ParameterMetadata[] = [];
    grupos.forEach((grupo: string) => {
        let parameters_: PreApprovedParametersGroup = { id: grupo } as PreApprovedParametersGroup;
        configParametersMapping.forEach((item: FormLinkWithId<ParametersModel, PreApprovedParameters>) => {
            const name_ = item.name as keyof PreApprovedParameters as string;

            const externalParameter = groupedData[grupo]
                .find((param: ParametersModel) => param.idParametros === item.id && param.idGrupo);

            if (!externalParameter) return;

            const valueOutput = item.toOutputValue(externalParameter as ParametersModel);
            parameters_ = { ...parameters_, parameters: { ...parameters_.parameters, [name_]: valueOutput } };
            parametersMetadata.push({
                fieldName: name_,
                fieldId: item.id,
                groupId: grupo,
                state: externalParameter.estado == "APROBADO" ? "approved" : "pending"
            });
        });
        preApprovedParametersGroup.push(parameters_);
    });

    const setFieldsMetadata = useFormStore.getState().setFieldsMetadata;
    setFieldsMetadata(parametersMetadata);

    return {
        grupos: preApprovedParametersGroup
    };
}

const mappingOutputParameters = (data: PreApprovedFormParameters, groupId: string, valoresFormulario : ParametersModel[]): ParametersModel[] => {
    let parameters: ParametersModel[] = [];
    const cleanGroup = data.grupos.filter((group) => group?.parameters);
    let formValido : boolean[] = [];
    cleanGroup.forEach((group) => {
        const groupParameters = group.parameters;
        const keysGroupParameters = Object.keys(groupParameters);

        keysGroupParameters.forEach((key) => {
            const name_ = key as keyof PreApprovedParameters;
            if (groupParameters[name_] !== null && groupParameters[name_] !== undefined) {
                const parameters_ = configParametersMapping.filter(c => c.name == name_);

                parameters_.forEach((item: FormLinkWithId<ParametersModel, PreApprovedParameters>) => {
                    const valueInput = item.toEntryValue(groupParameters);

                    let rangeValueEdadLimiteEssap = false;
                    let rangeValueEdadLimiteGral = false;
                    let rangeValueMontoPrestable = false;

                    if (item.name == 'minimoEdadLimite' || item.name == 'maximoEdadLimite') {
                        formValido.push(esParametroValido("minimoEdadLimite", "maximoEdadLimite", item, groupParameters, valoresFormulario, parameters, group.id));
                        rangeValueEdadLimiteGral = true;
                    }

                    if (item.name == 'minimoEdadLimiteEssap' || item.name == 'maximoEdadLimiteEssap') {
                        formValido.push(esParametroValido("minimoEdadLimiteEssap", "maximoEdadLimiteEssap", item, groupParameters, valoresFormulario, parameters, group.id));
                        rangeValueEdadLimiteEssap = true;
                    }

                    if (item.name == 'minimoMontoPrestableLimite' || item.name == 'maximoMontoPrestableLimite') {
                        formValido.push(esParametroValido("minimoMontoPrestableLimite", "maximoMontoPrestableLimite", item, groupParameters, valoresFormulario, parameters, group.id));
                        rangeValueMontoPrestable = true;
                    }

                    if (!rangeValueEdadLimiteEssap && !rangeValueEdadLimiteGral && !rangeValueMontoPrestable) {
                        parameters.push({
                            idParametros: item.id,
                            nombreParametro: item.parameterName,
                            tipoDato: "string",
                            estado: "APROBADO",
                            tipoValor: "U",
                            valorDesde: valueInput.toString(),
                            valorHasta: null,
                            listaValores: null,
                            idGrupo: group.id,
                        } as ParametersModel);
                    }
                });
            }
        });
    });

    return (formValido.includes(false)) ? [] : parameters;
};

const esParametroValido = (
    min: keyof PreApprovedParameters,
    max: keyof PreApprovedParameters,
    item: FormLinkWithId<ParametersModel, PreApprovedParameters>,
    preApprovedParameters: PreApprovedParameters,
    valoresFormulario: ParametersModel[],
    parameters: ParametersModel[],
    groupId: string
) => {
    if (item.name != min && item.name != max) return false;
    const valueInput = item.toEntryValue(preApprovedParameters);

    let rangoValor = {
        valorMin: '',
        valorMax: ''
    }

    valoresFormulario.forEach((objeto: ParametersModel) => {
        if (item.name != max && objeto.nombreParametro == item.parameterName) {
            rangoValor.valorMax = objeto.valorHasta?.toString() ?? ''
        } else if (item.name == max) {
            rangoValor.valorMax = valueInput;
        }

        if (item.name != min && objeto.nombreParametro == item.parameterName) {
            rangoValor.valorMin = objeto.valorDesde?.toString() ?? ''
        } else if (item.name == min) {
            rangoValor.valorMin = valueInput;
        }
    });

    if (utilHelper.esValido(rangoValor.valorMin) && utilHelper.esValido(rangoValor.valorMax)) {
        if (item.name == min && parseInt(rangoValor.valorMin) >= parseInt(rangoValor.valorMax)) {
            notify.warning(`Verificar: ${item.parameterName} \n El valor mínimo no puede ser mayor o igual al valor máximo.`)
            return false;
        }
    } else {
        notify.error('Ha ocurrido un error al validar el rango de los campos.');
        return false;
    }
    

    if (preApprovedParameters) {
        parameters.push({
            idParametros: item.id,
            nombreParametro: item.parameterName,
            tipoDato: "string",
            estado: "APROBADO",
            tipoValor: "R",
            valorDesde: rangoValor.valorMin.toString(),
            valorHasta: rangoValor.valorMax.toString(),
            listaValores: null,
            idGrupo: groupId,
            valorDesdePendiente: null,
            valorHastaPendiente: null
        });
    }

    return true;
}

export {
    mappingEntryParameters,
    mappingOutputParameters
};
