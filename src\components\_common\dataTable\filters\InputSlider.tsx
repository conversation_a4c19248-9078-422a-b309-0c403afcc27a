import { TValues } from '@/interfaces/_common';
import Paper from '@mui/material/Paper';
import Slider, { SliderValueLabelProps } from '@mui/material/Slider';
import TextField from '@mui/material/TextField';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Unstable_Grid2';
import { MRT_Header, MRT_TableInstance } from 'material-react-table';
import { useEffect, useRef, useState } from 'react';
import { NumberCurrencyFormat } from '../../NumberCurrencyFormat';
import { NumberFormat } from '../../NumberFormat';

interface InputSliderProps<TData extends TValues> {
    header: MRT_Header<TData>;
    table: MRT_TableInstance<TData>;
    min: number;
    max: number;
    isCurrency?: boolean;
}

export default function InputSlider<TData extends TValues>({ min, max, header, table, isCurrency = false }: InputSliderProps<TData>) {
    const { column } = header;

    const [filterValues, setFilterValues] = useState([min, max]);
    const [inputMinValue, setInputMinValue] = useState(min);
    const [inputMaxValue, setInputMaxValue] = useState(max);

    const [errorInput, setErrorInput] = useState([false, false]);
    const [errorMessage, setErrorMessage] = useState(["", ""]);

    const handleFilterValue = (event: Event, value: number | number[], activeThumb: number) => {
        if (Array.isArray(value)) {
            setFilterValues(value);
            setInputMinValue(value[0]);
            setInputMaxValue(value[1]);
        } else {
            const newFilterValues = [...filterValues];
            newFilterValues[activeThumb] = value;
            setFilterValues(newFilterValues);
        }
    }

    const handleBlurInputMin = () => {
        if (inputMinValue >= min && inputMinValue < max) {
            setFilterValues([inputMinValue, inputMaxValue])
            column.setFilterValue([inputMinValue, inputMaxValue]);
            setErrorInput(state => [false, state[1]]);
            setErrorMessage(["", errorMessage[1]]);
        } else {
            setErrorInput(state => [true, state[1]]);
            setErrorMessage(state => [`Mínimo ${min}`, state[1]]);
        }
    }

    const handleBlurInputMax = () => {
        if (inputMaxValue > min && inputMaxValue <= max) {
            setFilterValues([inputMinValue, inputMaxValue])
            column.setFilterValue([inputMinValue, inputMaxValue]);
            setErrorInput(state => [state[0], false]);
            setErrorMessage([errorMessage[0], ""]);
        } else {
            setErrorInput([false, true]);
            setErrorMessage([errorMessage[0], `Máximo: ${max}`]);
        }
    }
    const isMounted = useRef(false);
    const columnFilterValue = column.getFilterValue() as [number, number] | undefined;
    useEffect(() => {
        if (isMounted.current) {
            if (columnFilterValue === undefined) {
                setFilterValues([min, max]);
            }
        }
        isMounted.current = true;
    }, [columnFilterValue, min, max]);

    return (
        <Paper elevation={2} sx={{ padding: 1 }}>
            <Typography fontSize={"15px"}>
                {column.columnDef.header}
            </Typography>
            <Grid container alignItems="center" justifyContent={"center"}>
                <Grid xs={11}>
                    <Slider
                        disableSwap
                        min={min}
                        max={max}
                        
                        value={filterValues}
                        valueLabelDisplay='on'
                        slots={{
                            valueLabel: ValueLabelComponent,
                        }}
                        onChange={handleFilterValue}
                        onChangeCommitted={(_event, value) => {
                            if (Array.isArray(value)) {
                                if (value[0] <= min && value[1] >= max) {
                                    column.setFilterValue(undefined);
                                } else {
                                    column.setFilterValue(value as [number, number]);
                                }
                            }
                        }}
                        aria-labelledby="input-slider"
                    />
                </Grid>
                <Grid sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    gap: 1
                }}>
                    <TextField
                        variant="outlined"
                        size="small"
                        error={errorInput[0]}
                        helperText={errorMessage[0]}
                        InputProps={{
                            inputComponent: isCurrency ? NumberCurrencyFormat as any : NumberFormat as any,
                        }}
                        value={inputMinValue}
                        onChange={(event) => {
                            setInputMinValue(event.target.value as unknown as number);
                        }}
                        onBlur={handleBlurInputMin}
                    />
                    <TextField
                        variant="outlined"
                        size="small"
                        value={inputMaxValue}
                        error={errorInput[1]}
                        helperText={errorMessage[1]}
                        onChange={(event) => {
                            setInputMaxValue(event.target.value as unknown as number);
                        }}
                        onBlur={handleBlurInputMax}
                        InputProps={{
                            inputComponent: isCurrency ? NumberCurrencyFormat as any : NumberFormat as any
                        }}
                    />
                </Grid>
            </Grid>
        </Paper>
    );
}

function ValueLabelComponent(props: SliderValueLabelProps) {
    const { children, value } = props;
    return (
        <Tooltip enterTouchDelay={0} placement="top" title={value}>
            {children}
        </Tooltip>
    );
}