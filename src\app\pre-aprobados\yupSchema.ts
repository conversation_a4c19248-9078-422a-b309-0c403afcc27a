
import { OptionItem, OptionItemCreatable, OptionListGridItem } from '@/interfaces/_common';
import { PreApprovedFormParameters, PreApprovedParameters, PreApprovedParametersGroup } from '@/interfaces/parameters';
import { ObjectSchema, array, boolean, number, object, string } from 'yup';

const systemRatingSchema = string().required();
const delayReferenceSchema = string().required();
const averageConceptAcceptedSchema = object<OptionItemCreatable>({
    value: string().required(),
    inputValue: string().optional(),
    isNew: boolean().optional()
}).required();

const exclusionEmpresas = object<OptionListGridItem>({
    label: string().required(),
    value: string().required(),
    isDeleted: boolean().optional(),
    isNew: boolean().optional()
}).required();

const exclusionSucursales = object<OptionListGridItem>({
    label: string().required(),
    value: string().required(),
    isDeleted: boolean().optional(),
    isNew: boolean().optional()
}).required();

//@ts-ignore
const schemaParameters: ObjectSchema<PreApprovedParameters> = object({
    idGrupo: string().optional(),
    calificacionSisFinanciero: array(systemRatingSchema).optional(),
    refAtraso: array(delayReferenceSchema).optional(),
    exclusionEmpresas: array<OptionListGridItem>(exclusionEmpresas).optional(),
    exclusionSucursales: array<OptionListGridItem>(exclusionSucursales),
    ALDnegativo: boolean().optional(),
    riesgoOperacionSospechosa: boolean().optional(),
    montoLimite: number().optional().typeError("Debe ingresar un monto límite"),
    maximoEdadLimite: number().optional().typeError("Debe ingresar un máximo de edad límite"),
    minimoEdadLimite: number().optional().typeError("Debe ingresar un mínimo de edad límite"),
    maximoEdadLimiteEssap: number().optional().typeError("Debe ingresar un máximo de edad límite"),
    minimoEdadLimiteEssap: number().optional().typeError("Debe ingresar un mínimo de edad límite"),
    promedioAcreditacion: number().optional().typeError("Debe ingresar un promedio de acreditación"),
    antiguedad: number().optional().typeError("Debe ingresar una antigüedad"),
    rangoInforcomf: string().optional().typeError("Debe seleccionar un rango de inforcomf"),
    nivelEndeudamiento: number().typeError("Debe ingresar un nivel de endeudamiento"),
    calificacionBcp: array(string().required()).optional(),
    tieneMora: boolean().optional(),
    cantidadPrestamos: number().optional()
        .typeError("Debe ingresar una cantidad de préstamos"),
    coeficienteGastosAdministrativos: number().optional()
        .typeError("Debe ingresar un coeficiente de gastos administrativos"),
    seguroCoeficiente: string().optional(),
    tipoCliente: object<OptionItem>({
        value: string().optional(),
        label: string().optional()
    }).optional(), poseeCuentaGs: string().optional(),
    esCodeudor: boolean().optional(),
    conceptosAceptadosParaPromedio: array<OptionItemCreatable>(averageConceptAcceptedSchema).optional(),
    promedioPeriodoAcreditacion: number().optional()
        .typeError("Debe ingresar un promedio de periodo de acreditación"),
    porcentajePagoCredito: number().optional().typeError("Debe ingresar un porcentaje de pago de crédito"),
    minimoMontoPrestableLimite: number().optional().typeError("Debe ingresar un mínimo de monto prestable límite"),
    maximoMontoPrestableLimite: number().optional().typeError("Debe ingresar un máximo de monto prestable límite"),
    inhabilitadoInfocheck: boolean().optional(),
    nivelEndeudamientoEmpresasGrupo: number().optional().typeError("Debe ingresar un nivel de endeudamiento de empresas grupo"),
    montoMinimoPrestamosPreaprobados: number().optional().typeError("Debe ingresar un monto mínimo de préstamos preaprobados")
});

const schemaGruposParametros: ObjectSchema<PreApprovedParametersGroup> = object({
    id: string().required(),
    parameters: schemaParameters
});

export const schemaFormParameters: ObjectSchema<PreApprovedFormParameters> = object({
    grupos: array(schemaGruposParametros).required()
});