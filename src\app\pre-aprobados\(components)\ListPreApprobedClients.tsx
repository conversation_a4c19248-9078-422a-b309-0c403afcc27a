import * as React from 'react';
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Typography,
  CircularProgress,
  Popover
} from '@mui/material';
import { CustomTablePagination, Root } from '../(styles)/estilosPaginacionTabla';
import FirstPageRoundedIcon from '@mui/icons-material/FirstPageRounded';
import LastPageRoundedIcon from '@mui/icons-material/LastPageRounded';
import ChevronLeftRoundedIcon from '@mui/icons-material/ChevronLeftRounded';
import ChevronRightRoundedIcon from '@mui/icons-material/ChevronRightRounded';
import SearchIcon from '@mui/icons-material/Search';
import dayjs from 'dayjs';
import { preApprovedService } from '@/services/preApprovedService';
import RevalidateMenu from './RevalidateMenu';
import ModalFiltros from '../(modals)/ModalFiltros';
import FilterListIcon from '@mui/icons-material/FilterList';
import HelpIcon from '@mui/icons-material/Help';
import { utilHelper } from '@/helpers/utilHelper';
import { searchServices } from '@/services/searchServices';
import { SetStateAction, useEffect, useState, MouseEvent } from 'react';
import FileOpenIcon from '@mui/icons-material/FileOpen';
import { TIPO_FILTRO_LISTA_PREAPROBADOS } from '../(enums)/FormPreAprobadosPgs';
import UseListPreAprobados from '../(hooks)/UseListPreAprobados';
import LoaderHelper from '@/helpers/loaderHelper';
import { TITULO_LOADER } from '../(enums)/LoaderEnum';
import notify from '@/helpers/notifyHelper';
import { ORIGEN_CONSULTA } from '../(enums)/UtilEnum';
interface Column {
  id: string;
  label: string;
  minWidth?: number;
  align?: string;
  format?: (value: number) => string;
}

const ListPreApprovedClients = () => {
  const columns: readonly Column[] = [
    { id: 'codigoCliente', label: 'Codigo del Cliente', minWidth: 100, align: 'center' },
    { id: 'cuenta', label: 'Cuenta', minWidth: 100, align: 'center' },
    { id: 'revalidado', label: 'Fue Revalidado?', minWidth: 100, align: 'center' },
    { id: 'fechaRevalidacion', label: 'Fecha Revalidacion', minWidth: 100, align: 'center' },
    { id: 'nombreCampanha', label: 'Campaña', minWidth: 170, align: 'center' },
    {
      id: 'promedioAcreditacion',
      label: 'Promedio de Acreditacion',
      minWidth: 150,
      align: 'center',
    },
    {
      id: 'porcentajeEndeudamiento',
      label: '% Deudas',
      minWidth: 100,
      align: 'center',
    },
    {
      id: 'montoPrestamo',
      label: 'Monto Prestamo Máximo',
      minWidth: 170,
      align: 'center',
    },
    { id: 'edad', label: 'Edad', minWidth: 100, align: 'center' },
    {
      id: 'antiguedad',
      label: 'Antiguedad',
      minWidth: 150,
      align: 'center',
    },
    { id: 'calificacionConti', label: 'Calif. Conti', minWidth: 100, align: 'center' },
    { id: 'inforcomf', label: 'Calif. Servicio externo', minWidth: 100, align: 'center' },
    { id: 'estadoInformconf', label: 'Estado servicio externo', minWidth: 150, align: 'center' },
    { id: 'calificacionBCP', label: 'Calif. BCP', minWidth: 100, align: 'center' },
    { id: 'estadoBCP', label: 'Estado BCP', minWidth: 100, align: 'center' },
    { id: 'referenciaAtraso', label: 'Atraso', minWidth: 100, align: 'center' },
    { id: 'codeudor', label: 'Codeudor', minWidth: 100, align: 'center' },
    { id: 'mora', label: 'Mora', minWidth: 100, align: 'center' },
    { id: 'sucursal', label: 'Sucursal', minWidth: 100, align: 'center' },
    { id: 'ald', label: 'Lista Negativa', minWidth: 400, align: 'left' },
    { id: 'ros', label: 'ROS', minWidth: 100, align: 'center' },
    {
      id: 'cantidadPrestamo',
      label: 'Cantidad del prestamo',
      minWidth: 150,
      align: 'center',
    },
  ];
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(30);
  const [rows, setRows] = useState([]);
  const [totalRows, setTotalRows] = useState(0);
  const [filters, setFilters] = useState({
    codigoCliente: '',
    cuenta: '',
    periodo: '',
    nombreCampanha: '',
    promedioAcreditacionMin: 0,
    promedioAcreditacionMax: 0,
    porcentajeEndeudamientoMin: 0,
    porcentajeEndeudamientoMax: 0,
    montoPrestamoMin: 0,
    montoPrestamoMax: 0,
    edadMin: 0,
    edadMax: 0,
    antiguedadMin: 0,
    antiguedadMax: 0,
    estadoInformconf: '',
    ald: '',
    codeudor: '',
    calificacionBCP: '',
    estadoBCP: '',
    calificacionConti: '',
    referenciaAtraso: '',
    sucursal: '',
    inforcomf: '',
    cantidadPrestamo: 0,
    revalidado: '',
    fechaRevalidacion: ''
  });
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const open = Boolean(anchorEl);
  const [filterDialogOpen, setFilterDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [sucursales, setSucursales] = useState([]);
  const idPopOver = open ? 'popOverAyuda' : undefined;7
  const [resetDone, setResetDone] = useState(false);

  const formatNumber = (value: number) => {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
  };

  const getEstadoChip = (valor: any) => {
    if(!utilHelper.esValido(valor)) return '';
    if (valor === 'PENDIENTE') {
      return <Chip label={valor} color="warning" />;
    } else if (valor == 'LISTO') {
      return <Chip label={valor} color="success" />;
    } else {
      return valor;
    }
  };

  const getRevalidadoChip = (valor: any) => {
    if(!utilHelper.esValido(valor)) return '';
    if (valor === 'SI') {
      return <Chip label={valor} color="success" />;
    } else if (valor == 'NO') {
      return <Chip label={valor} color="warning" />;
    } else {
      return valor;
    }
  };

  const formatFechaRevalidacion = (valor: any) => {
    if(!utilHelper.esValido(valor)) return '';
    return dayjs(valor).format('DD/MM/YYYY');
  }

  const formatCuentaChip = (valor: any) => {
    if(!utilHelper.esValido(valor)) return '';
    return <Chip label={valor} color="primary" />;
  };

  const formatColumnValue = (columna: string, valor: any) => {
    return formatFunctions[columna] ? formatFunctions[columna](valor) : valor;
  };

  const formatSucursalChip = (valor: any) => {
    if(!utilHelper.esValido(valor)) return '';

    let tagSucursal: any = sucursales.find((s: any) => s.codigo === valor);
    if (utilHelper.esValido(tagSucursal)) {
      return <Chip label={tagSucursal?.descripcion} color="primary" variant="outlined" />;
    }

    return valor;
  }
  
  const formatFunctions : { [key: string]: (valor: any) => JSX.Element | string } = {
    cuenta: formatCuentaChip,
    estadoInformconf: getEstadoChip,
    estadoBCP: getEstadoChip,
    promedioAcreditacion: (valor) => `Gs. ${formatNumber(valor)}`,
    montoPrestamo: (valor) => `Gs. ${formatNumber(valor)}`,
    porcentajeEndeudamiento: (valor) => `${valor > '0' ? parseFloat(valor).toFixed(2) : '0'} %`,
    antiguedad: (valor) => `${valor} Meses`,
    sucursal: formatSucursalChip,
    revalidado: getRevalidadoChip,
    fechaRevalidacion: formatFechaRevalidacion
  };

  const fetchData = async () => {
    try {
      setLoading(true);
      const params = {
        ...filters,
        numeroPaginaActual: page + 1,
        cantidadRegistrosPagina: rowsPerPage,
        periodo: utilHelper.calculatePeriodo()
      };

      const respClientes: any = await preApprovedService.getInternalPreApprovedClients(params);

      if (utilHelper.esValido(respClientes?.data?.datos)) {
        setRows(respClientes.data?.datos);
        setTotalRows(respClientes.data?.totalRegistros);
      } else {
        setRows([]);
        setTotalRows(0);
      }
      setLoading(false);
    } catch (error) {
      console.error('Error durante la consulta de datos: ', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSucursales = async () => {
    try {
      setLoading(true);

      const respSucursales: any = await searchServices.branches();

      if (utilHelper.esValido(respSucursales?.data)) {
        setSucursales(respSucursales?.data);
      }
      setLoading(false);
    } catch (error) {
      notify.error("Ocurrio un error durante la consulta de sucursales disponibles.");
      console.error('Error durante la consulta de datos: ', error);
    } finally {
      setLoading(false);
    }
  };

  const resetFilters = () => {
    setFilters({
      codigoCliente: '',
      cuenta: '',
      nombreCampanha: '',
      promedioAcreditacionMin: 0,
      promedioAcreditacionMax: 0,
      porcentajeEndeudamientoMin: 0,
      porcentajeEndeudamientoMax: 0,
      montoPrestamoMin: 0,
      montoPrestamoMax: 0,
      edadMin: 0,
      edadMax: 0,
      antiguedadMin: 0,
      antiguedadMax: 0,
      estadoInformconf: '',
      ald: '',
      codeudor: '',
      calificacionBCP: '',
      estadoBCP: '',
      calificacionConti: '',
      referenciaAtraso: '',
      inforcomf: '',
      cantidadPrestamo: 0,
      sucursal: '',
      periodo: '',
      revalidado: '',
      fechaRevalidacion: ''
    });
  };

  useEffect(() => {
    fetchSucursales();
  }, [])

  useEffect(() => {
    fetchData();
  }, [page, rowsPerPage]);

  const handleChangePage = (event: any, newPage: SetStateAction<number>) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: { target: { value: string | number; }; }) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  const handleFilterChange = (tipoFiltro: string, name: string, value: string | number) => {
    let formattedValue = value;
    if (typeof value === 'string' && tipoFiltro == TIPO_FILTRO_LISTA_PREAPROBADOS.FILTRAR_MONTO) {
      formattedValue = parseFloat(value.replace(/\./g, ''));
    }
    setFilters((prevFilters) => ({
      ...prevFilters,
      [name]: formattedValue,
    }));
  };

  const handleSearch = async () => {
    resetFilters();
    await fetchData();
  };

  const handleSearchFilter = async () => {
    setFilterDialogOpen(false);
    await fetchData();
  };

  const handleClickPopover = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClosePopover = () => {
    setAnchorEl(null);
  };

  const handleSearchResetAll = async () => {
    resetFilters();
    setResetDone(true);
  };

  useEffect(() => {
    if (resetDone) {
      handleSearchFilter();
      setResetDone(false);
    }
  }, [resetDone]);

  const { confirmarExportacion, informacionLoader, mostrarLoaderExcel } = UseListPreAprobados({onFormatColumnValue: formatColumnValue, filters, columns, origenExportacion: ORIGEN_CONSULTA.LISTADO });

  return (
    <>
      { mostrarLoaderExcel ? (
        <LoaderHelper tituloLoader={TITULO_LOADER.EXPORTAR_EXCEL} contenido={informacionLoader()} mostrarLoader={mostrarLoaderExcel} />
      ) : null}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
        <Box sx={{ display: 'flex', justifyContent: 'flex-start' }}>
          <Popover
            id={idPopOver}
            open={open}
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            onClose={handleClosePopover}
            sx={{ padding: 0, zIndex: 1200 }}
          >
            <Paper sx={{ backgroundColor: '#195AA7', color: 'white', display: 'flex', flexDirection: 'column' }}>
              <Typography sx={{ paddingX: 2, paddingY: 2 }}>
                <li>Mantenga presionada la tecla SHIFT y utilice la rueda del MOUSE para navegar horizontalmente a través de la tabla.</li>
              </Typography>
            </Paper>
          </Popover>
          <Button aria-describedby='popover-ayuda' variant="text" color='primary' onClick={handleClickPopover} endIcon={<HelpIcon />} sx={{ fontSize: '18px' }}>
            Ayuda
          </Button>
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', padding: 2, gap: 2 }}>
          <RevalidateMenu
            onFetchData={fetchData}
          />
          <Button variant="contained" color="primary" startIcon={<FilterListIcon />} onClick={() => setFilterDialogOpen(true)}>
            Filtrar
          </Button>
          <Button color="primary" variant="contained" startIcon={<SearchIcon />} onClick={handleSearchResetAll}>
            Buscar Todo
          </Button>
          <Button variant="contained" color="success" startIcon={<FileOpenIcon />} onClick={confirmarExportacion}>
            Exportar a Excel
          </Button>
        </Box>
      </Box>

      <ModalFiltros
        open={filterDialogOpen}
        onClose={() => setFilterDialogOpen(false)}
        onSearch={handleSearchFilter}
        onFilterChange={handleFilterChange}
        filters={filters}
        resetFilters={resetFilters}
        sucursalesData={sucursales}
        origenConsultaFiltro={ORIGEN_CONSULTA.LISTADO}
        onSearchResetAll={handleSearchResetAll}
      />
      <Paper sx={{ width: '100%', overflow: 'hidden', marginTop: 6 }} elevation={2}>
        <TableContainer sx={{ maxHeight: 440 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 440 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Table stickyHeader aria-label="sticky table">
              <TableHead>
                <TableRow>
                  {columns.map((column) => (
                    <TableCell
                      key={column.id}
                      align={column.align as any}
                      style={{ minWidth: column.minWidth }}
                      sx={{ fontWeight: 'bold' }}
                    >
                      {column.label}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {rows.length === 0 && !loading ? (
                  <TableRow>
                    <TableCell colSpan={columns.length + 1} align="left">
                      No se encontraron registros.
                    </TableCell>
                  </TableRow>
                ) : (
                  rows.map((row: any) => {
                    return (
                      <TableRow
                        hover
                        tabIndex={-1}
                        key={row.codigoCliente}
                        sx={{ cursor: 'pointer' }}
                      >
                        {columns.map((column: Column) => {
                          const value = row[column.id];
                          return (
                            <TableCell key={column.id} align={column.align as any}>
                              {formatColumnValue(column.id, value)}
                            </TableCell>
                          );
                        })}
                      </TableRow>
                    );
                  }))}
              </TableBody>
            </Table>
          )}
        </TableContainer>

        <Root>
          <table aria-label="custom pagination table">
            <tfoot>
              <tr>
                <CustomTablePagination
                  rowsPerPageOptions={[5, 10, 25, 30]}
                  colSpan={3}
                  count={totalRows}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  labelRowsPerPage="Registros por página"
                  slotProps={{
                    select: {
                      style: {
                        padding: '12px 25px',
                      },
                    },
                    actions: {
                      showFirstButton: true,
                      showLastButton: true,
                      slots: {
                        firstPageIcon: FirstPageRoundedIcon,
                        lastPageIcon: LastPageRoundedIcon,
                        nextPageIcon: ChevronRightRoundedIcon,
                        backPageIcon: ChevronLeftRoundedIcon,
                      },
                    },
                  }}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                />
              </tr>
            </tfoot>
          </table>
        </Root>
      </Paper>
    </>
  );
};

export default ListPreApprovedClients;