import { NUMBER_FORMAT } from '@/constants/_common';
import React from 'react';
import { NumericFormat } from "react-number-format";

interface NumberFormatProps {
    name: string;
    onChange: (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}
export const NumberFormat = React.forwardRef(function NumberFormatCustom(props: NumberFormatProps, ref) {
    const { onChange, ...other } = props;

    return (
        <NumericFormat
            {...other}
            getInputRef={ref}
            onValueChange={(values) => {
                onChange({
                    target: {
                        name: props.name,
                        value: values.value,
                    },
                } as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>);
            }}
            thousandSeparator={NUMBER_FORMAT.thousandSeparator}
            decimalScale={NUMBER_FORMAT.decimalScale}
            decimalSeparator={NUMBER_FORMAT.decimalSeparator}
            valueIsNumericString
        />
    );
});