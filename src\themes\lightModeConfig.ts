import { createTheme } from "@mui/material/styles";

const lightThemeConfig = createTheme({
    typography: {
        fontFamily: [
          'ContiSans'
        ].join(','),
    },
    palette: {
        mode: "light",
        primary: {
            main: "#1D428A",
        },
    },
    components: {
        MuiInputBase: {
            defaultProps: {
                disableInjectingGlobalStyles: true,
            },
        },
    },
});

export default lightThemeConfig;