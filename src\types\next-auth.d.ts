import NextAuth, { DefaultSession, ISODateString } from "next-auth"

declare module "next-auth" {
    interface User extends DefaultUser {
    }

    interface Session {
        user: {
            address: string;
        } & DefaultSession["user"],
        expires: ISODateString,
    }

    interface Profile {
        preferred_username: string,
    }
}

declare module "next-auth/jwt" {
    interface JWT {
        accessToken?: string
        idToken?: string
    }
}