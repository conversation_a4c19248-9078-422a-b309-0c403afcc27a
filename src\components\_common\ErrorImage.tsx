import AbiFeliz from '@/assets/images/abi/abi-feliz.svg'
import AbiInterrogacion from '@/assets/images/abi/abi-interrogacion.svg'
import AbiSaludando from '@/assets/images/abi/abi-saludando.svg'
import AbiSeguridad from '@/assets/images/abi/abi-seguridad.svg'
import AbiTriste from '@/assets/images/abi/abi-triste.svg'
import { EAbiType } from '@/interfaces/utils'
import Image from 'next/image'

const selectAbiByType = (type: "feliz" | "interrogacion" | "saludando" | "seguridad" | "triste") => {
    switch (type) {
        case "feliz":
            return AbiFeliz
        case "interrogacion":
            return AbiInterrogacion
        case "saludando":
            return AbiSaludando
        case "seguridad":
            return AbiSeguridad
        case "triste":
            return AbiTriste
        default:
            return AbiTriste
    }
}

interface ErrorImageProps {
    abiType?: EAbiType
}
const ErrorImage = ({ abiType }: ErrorImageProps) => {
    let image = <Image src={selectAbiByType(abiType ?? "triste")} alt="abi" width={140} />

    return (
        <div className="flex justify-center items-center">
            {image}
        </div>
    )
}

export default ErrorImage