"use client"
import { Button, Chip, CircularProgress, Paper, Popover, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from '@mui/material';
import Box from '@mui/material/Box';
import { SetStateAction, useEffect, useState, MouseEvent } from 'react';
import notify from '@/helpers/notifyHelper';
import { utilHelper } from '@/helpers/utilHelper';
import { preApprovedService } from '@/services/preApprovedService';
import { CustomTablePagination, Root } from '../(styles)/estilosPaginacionTabla';
import FirstPageRoundedIcon from '@mui/icons-material/FirstPageRounded';
import LastPageRoundedIcon from '@mui/icons-material/LastPageRounded';
import ChevronLeftRoundedIcon from '@mui/icons-material/ChevronLeftRounded';
import ChevronRightRoundedIcon from '@mui/icons-material/ChevronRightRounded';
import { searchServices } from '@/services/searchServices';
import FileOpenIcon from '@mui/icons-material/FileOpen';
import FilterListIcon from '@mui/icons-material/FilterList';
import SearchIcon from '@mui/icons-material/Search';
import UseListPreAprobados from '../(hooks)/UseListPreAprobados';
import { ESTADOS, ORIGEN_CONSULTA } from '../(enums)/UtilEnum';
import { TITULO_LOADER } from '../(enums)/LoaderEnum';
import LoaderHelper from '@/helpers/loaderHelper';
import HelpIcon from '@mui/icons-material/Help';
import ModalFiltros from '../(modals)/ModalFiltros';
import { TIPO_FILTRO_LISTA_PREAPROBADOS } from '../(enums)/FormPreAprobadosPgs';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import dayjs, { Dayjs } from 'dayjs';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import 'dayjs/locale/es';
import { PreApprovedClientsModel } from '@/models/responses/clientServices';

interface Column {
    id: string;
    label: string;
    minWidth?: number;
    align?: string;
    format?: (value: number) => string;
}

const ListHistoryPreApprobedClients = () => {
    //* VARIABLES
    const [parametrosSeleccionados, setParametrosSeleccionados] = useState<any | null>(null);
    const [modalParametrosOpen, setModalParametrosOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const columns: readonly Column[] = [
        { id: 'codigoCliente', label: 'Codigo del Cliente', minWidth: 100, align: 'center' },
        { id: 'cuenta', label: 'Cuenta', minWidth: 100, align: 'center' },
        { id: 'eliminadoPorRevalidacion', label: 'Fue eliminado?', minWidth: 100, align: 'center' },
        { id: 'nombreCampanha', label: 'Campaña', minWidth: 170, align: 'center' },
        {
            id: 'promedioAcreditacion',
            label: 'Promedio de Acreditacion',
            minWidth: 150,
            align: 'center',
        },
        {
            id: 'porcentajeEndeudamiento',
            label: '% Deudas',
            minWidth: 100,
            align: 'center',
        },
        {
            id: 'montoPrestamo',
            label: 'Monto Prestamo Máximo',
            minWidth: 170,
            align: 'center',
        },
        { id: 'edad', label: 'Edad', minWidth: 100, align: 'center' },
        {
            id: 'antiguedad',
            label: 'Antiguedad',
            minWidth: 150,
            align: 'center',
        },
        { id: 'calificacionConti', label: 'Calif. Conti', minWidth: 100, align: 'center' },
        { id: 'inforcomf', label: 'Calif. Servicio externo', minWidth: 100, align: 'center' },
        { id: 'estadoInformconf', label: 'Estado servicio externo', minWidth: 150, align: 'center' },
        { id: 'calificacionBCP', label: 'Calif. BCP', minWidth: 100, align: 'center' },
        { id: 'estadoBCP', label: 'Estado BCP', minWidth: 100, align: 'center' },
        { id: 'referenciaAtraso', label: 'Atraso', minWidth: 100, align: 'center' },
        { id: 'codeudor', label: 'Codeudor', minWidth: 100, align: 'center' },
        { id: 'mora', label: 'Mora', minWidth: 100, align: 'center' },
        { id: 'sucursal', label: 'Sucursal', minWidth: 100, align: 'center' },
        { id: 'ald', label: 'Lista Negativa', minWidth: 400, align: 'left' },
        { id: 'ros', label: 'ROS', minWidth: 100, align: 'center' },
        {
            id: 'cantidadPrestamo',
            label: 'Cantidad del prestamo',
            minWidth: 150,
            align: 'center',
        },
        { id: 'parametrosHistoricos', label: 'Parametros Utilizados', minWidth: 100, align: 'center' },
    ];
    const [filters, setFilters] = useState({
        codigoCliente: '',
        cuenta: '',
        periodo: utilHelper.calculatePeriodo(),
        eliminadoPorRevalidacion: '',
        nombreCampanha: '',
        promedioAcreditacionMin: 0,
        promedioAcreditacionMax: 0,
        porcentajeEndeudamientoMin: 0,
        porcentajeEndeudamientoMax: 0,
        montoPrestamoMin: 0,
        montoPrestamoMax: 0,
        edadMin: 0,
        edadMax: 0,
        antiguedadMin: 0,
        antiguedadMax: 0,
        estadoInformconf: '',
        ald: '',
        codeudor: '',
        calificacionBCP: '',
        estadoBCP: '',
        calificacionConti: '',
        referenciaAtraso: '',
        sucursal: '',
        inforcomf: '',
        cantidadPrestamo: 0
    });
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(30);
    const [rows, setRows] = useState([]);
    const [totalRows, setTotalRows] = useState(0);
    const [filterDialogOpen, setFilterDialogOpen] = useState(false);
    const [sucursales, setSucursales] = useState([]);
    const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
    const open = Boolean(anchorEl);
    const idPopOver = open ? 'popOverAyuda' : undefined;
    const [resetDone, setResetDone] = useState(false);
    dayjs.locale('es');

    useEffect(() => {
        fetchData();
    }, [page, rowsPerPage]);

    useEffect(() => {
        fetchSucursales();
    }, [])

    useEffect(() => {
        if (resetDone) {
            handleSearch();
            setResetDone(false);
        }
    }, [resetDone]);

    const fetchData = async () => {
        try {
            setLoading(true);
            const params = {
                ...filters,
                numeroPaginaActual: page + 1,
                cantidadRegistrosPagina: rowsPerPage
            };

            const respClientes: any = await preApprovedService.getInternalPreApprovedClientsHistory(params);

            if (utilHelper.esValido(respClientes?.data?.datos)) {
                setRows(respClientes.data?.datos);
                setTotalRows(respClientes.data?.totalRegistros);
            } else {
                setRows([]);
                setTotalRows(0);
            }
            setLoading(false);
        } catch (error) {
            console.error('Error durante la consulta de datos: ', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchSucursales = async () => {
        try {
            setLoading(true);

            const respSucursales: any = await searchServices.branches();

            if (utilHelper.esValido(respSucursales?.data)) {
                setSucursales(respSucursales?.data);
            }
            setLoading(false);
        } catch (error) {
            notify.error("Ocurrio un error durante la consulta de sucursales disponibles.");
            console.error('Error durante la consulta de datos: ', error);
        } finally {
            setLoading(false);
        }
    };

    const handleChangePage = (event: any, newPage: SetStateAction<number>) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event: { target: { value: string | number; }; }) => {
        setRowsPerPage(+event.target.value);
        setPage(0);
    };

    const resetFilters = () => {
        setFilters({
            codigoCliente: '',
            cuenta: '',
            periodo: utilHelper.calculatePeriodo(),
            eliminadoPorRevalidacion: '',
            nombreCampanha: '',
            promedioAcreditacionMin: 0,
            promedioAcreditacionMax: 0,
            porcentajeEndeudamientoMin: 0,
            porcentajeEndeudamientoMax: 0,
            montoPrestamoMin: 0,
            montoPrestamoMax: 0,
            edadMin: 0,
            edadMax: 0,
            antiguedadMin: 0,
            antiguedadMax: 0,
            estadoInformconf: '',
            ald: '',
            codeudor: '',
            calificacionBCP: '',
            estadoBCP: '',
            calificacionConti: '',
            referenciaAtraso: '',
            inforcomf: '',
            cantidadPrestamo: 0,
            sucursal: ''
        });
    };

    const handleSearch = async () => {
        setFilterDialogOpen(false);
        await fetchData();
    };
    
    const handleFilterChange = (tipoFiltro: string, name: string, value: string | number) => {
        let formattedValue = value;
        if (typeof value === 'string' && tipoFiltro == TIPO_FILTRO_LISTA_PREAPROBADOS.FILTRAR_MONTO) {
            formattedValue = parseFloat(value.replace(/\./g, ''));
        }
        setFilters((prevFilters) => ({
            ...prevFilters,
            [name]: formattedValue,
        }));
    };

    //* FORMATEOS
    const getEstadoChip = (valor: any) => {
        if (!utilHelper.esValido(valor)) return '';
        if (valor === ESTADOS.PENDIENTE) {
            return <Chip label={valor} color="warning" />;
        } else if (valor == ESTADOS.LISTO) {
            return <Chip label={valor} color="success" />;
        } else {
            return valor;
        }
    };

    const getEliminadoPorRevalidacionChip = (valor: any) => {
        if (!utilHelper.esValido(valor)) return '';
        if (valor === ESTADOS.NO) {
            return <Chip label={valor} color="warning" />;
        } else if (valor == ESTADOS.SI) {
            return <Chip label={valor} color="success" />;
        } else {
            return valor;
        }
    };

    const formatNumber = (value: number) => {
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    };

    const formatCuentaChip = (valor: any) => {
        if (!utilHelper.esValido(valor)) return '';
        return <Chip label={valor} color="primary" />;
    };

    const formatColumnValue = (columna: string, valor: any, row?: any) => {
        return formatFunctions[columna] ? formatFunctions[columna](valor, row) : valor;
    };

    const formatSucursalChip = (valor: any) => {
        if (!utilHelper.esValido(valor)) return '';

        let tagSucursal: any = sucursales.find((s: any) => s.codigo === valor);
        if (utilHelper.esValido(tagSucursal)) {
            return <Chip label={tagSucursal?.descripcion} color="primary" variant="outlined" />;
        }

        return valor;
    }

    const formatParametrosUtilizadosButton = (row: PreApprovedClientsModel) => {
        return (
            <Button
                variant="outlined"
                color="primary"
                size="small"
                onClick={() => handleVerParametros(row)}
            >
                VER
            </Button>
        );
    };

    const formatFunctions: { [key: string]: (valor: any, row?: any) => JSX.Element | string } = {
        cuenta: formatCuentaChip,
        estadoInformconf: getEstadoChip,
        estadoBCP: getEstadoChip,
        promedioAcreditacion: (valor) => `Gs. ${formatNumber(valor)}`,
        montoPrestamo: (valor) => `Gs. ${formatNumber(valor)}`,
        porcentajeEndeudamiento: (valor) => `${valor > '0' ? parseFloat(valor).toFixed(2) : '0'} %`,
        antiguedad: (valor) => `${valor} Meses`,
        sucursal: formatSucursalChip,
        eliminadoPorRevalidacion: getEliminadoPorRevalidacionChip,
        parametrosHistoricos: (_, row) => formatParametrosUtilizadosButton(row)
    };

    const handleClickPopover = (event: MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClosePopover = () => {
        setAnchorEl(null);
    };

    const handleDateChange = (newValue: Dayjs | null) => {
        if (newValue) {
            const fechaFormateada = newValue.format('YYYYMM');
            handleFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, 'periodo', fechaFormateada);
        } else {
            handleFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, 'periodo', utilHelper.calculatePeriodo());
        }
    };

    const handleSearchResetAll = async () => {
        resetFilters();
        setResetDone(true);
    };

    const handleVerParametros = async (row: PreApprovedClientsModel) => {
        const parametroHistoricos: any = await preApprovedService.getHistoricParameters();
        const parametros: any = await preApprovedService.getParameters('1', 'approved');

        let resultado = parametroHistoricos.data.count > 0 ? 

        parametroHistoricos.data
        .filter((item: any) => item.idHistoricoRegla === row.idRegla)
        .map((item: any) => ({
            nombreParametro: item.nombreParametro,
            valor: item.valorDesde,
        }))

        : parametros.data
        .map((item: any) => ({
            nombreParametro: item.nombreParametro,
            valor: item.valorDesde,
        }));
        
        setParametrosSeleccionados(resultado);
        setModalParametrosOpen(true);
    };


    const { confirmarExportacion, informacionLoader, mostrarLoaderExcel } = UseListPreAprobados({ onFormatColumnValue: formatColumnValue, filters, columns, origenExportacion: ORIGEN_CONSULTA.HISTORICO });

    return (
        <>
            { mostrarLoaderExcel ? (
                <LoaderHelper tituloLoader={TITULO_LOADER.EXPORTAR_EXCEL} contenido={informacionLoader()} mostrarLoader={mostrarLoaderExcel} />
            ) : null }
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-start' }}>
                    <Popover
                        id={idPopOver}
                        open={open}
                        anchorEl={anchorEl}
                        anchorOrigin={{
                            vertical: 'bottom',
                            horizontal: 'right',
                        }}
                        transformOrigin={{
                            vertical: 'top',
                            horizontal: 'left',
                        }}
                        onClose={handleClosePopover}
                        sx={{ padding: 0, zIndex: 1200 }}
                    >
                        <Paper sx={{ backgroundColor: '#195AA7', color: 'white', display: 'flex', flexDirection: 'column' }}>
                            <Typography sx={{ paddingX: 2, paddingY: 2 }}>
                                <li>Mantenga presionada la tecla SHIFT y utilice la rueda del MOUSE para navegar horizontalmente a través de la tabla.</li>
                                <li>Seleccione un mes y presione el botón BUSCAR para obtener los registros del histórico de clientes.</li>
                            </Typography>
                        </Paper>
                    </Popover>
                    <Button aria-describedby='popover-ayuda' variant="text" color='primary' onClick={handleClickPopover} endIcon={<HelpIcon />} sx={{ fontSize: '18px' }}>
                        Ayuda
                    </Button>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', padding: 2, gap: 2 }}>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                        <DatePicker
                            label="Periodo"
                            value={filters.periodo ? dayjs(filters.periodo, 'YYYYMM') : null}
                            onChange={handleDateChange}
                            views={['month', 'year']}
                            openTo="month"
                            disableFuture
                            monthsPerRow={4}
                            slotProps={{ field: { clearable: true } }}
                        />
                    </LocalizationProvider>
                    <Button color="primary" variant="contained" startIcon={<SearchIcon />} onClick={handleSearch}>
                        Buscar
                    </Button>
                    <Button variant="contained" color="primary" startIcon={<FilterListIcon />} onClick={() => setFilterDialogOpen(true)}>
                        Filtrar
                    </Button>
                    <Button variant="contained" color="success" startIcon={<FileOpenIcon />} onClick={confirmarExportacion}>
                        Exportar a Excel
                    </Button>
                </Box>
            </Box>

            <ModalFiltros
                open={filterDialogOpen}
                onClose={() => setFilterDialogOpen(false)}
                onSearch={handleSearch}
                onFilterChange={handleFilterChange}
                filters={filters}
                resetFilters={resetFilters}
                sucursalesData={sucursales}
                origenConsultaFiltro={ORIGEN_CONSULTA.HISTORICO}
                onSearchResetAll={handleSearchResetAll}
            />

            <Paper sx={{ width: '100%', overflow: 'hidden', marginTop: 6 }} elevation={2}>
                <TableContainer sx={{ maxHeight: 440 }}>
                    {loading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 440 }}>
                            <CircularProgress />
                        </Box>
                    ) : (
                        <Table stickyHeader aria-label="sticky table">
                            <TableHead>
                                <TableRow>
                                    {columns.map((column) => (
                                        <TableCell
                                            key={column.id}
                                            align={column.align as any}
                                            style={{ minWidth: column.minWidth }}
                                            sx={{ fontWeight: 'bold' }}
                                        >
                                            {column.label}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {rows.length === 0 && !loading ? (
                                    <TableRow>
                                        <TableCell colSpan={columns.length + 1} align="left">
                                            No se encontraron registros.
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    rows.map((row: any) => {
                                        return (
                                            <TableRow
                                                hover
                                                tabIndex={-1}
                                                key={row.codigoCliente}
                                                sx={{ cursor: 'pointer' }}
                                            >
                                                {columns.map((column: Column) => {
                                                    const value = row[column.id];
                                                    return (
                                                        <TableCell key={column.id} align={column.align as any}>
                                                            {formatColumnValue(column.id, value, row)}
                                                        </TableCell>
                                                    );
                                                })}
                                            </TableRow>
                                        );
                                    }))}
                            </TableBody>
                        </Table>
                    )}
                </TableContainer>

                <Root>
                    <table aria-label="custom pagination table">
                        <tfoot>
                            <tr>
                                <CustomTablePagination
                                    rowsPerPageOptions={[5, 10, 25, 30]}
                                    colSpan={3}
                                    count={totalRows}
                                    rowsPerPage={rowsPerPage}
                                    page={page}
                                    labelRowsPerPage="Registros por página"
                                    slotProps={{
                                        select: {
                                            style: {
                                                padding: '12px 25px',
                                            },
                                        },
                                        actions: {
                                            showFirstButton: true,
                                            showLastButton: true,
                                            slots: {
                                                firstPageIcon: FirstPageRoundedIcon,
                                                lastPageIcon: LastPageRoundedIcon,
                                                nextPageIcon: ChevronRightRoundedIcon,
                                                backPageIcon: ChevronLeftRoundedIcon,
                                            },
                                        },
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage} />
                            </tr>
                        </tfoot>
                    </table>
                </Root>
            </Paper>
            {modalParametrosOpen && (
                <Paper sx={{
                    position: 'fixed',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: 800,
                    maxHeight: '80vh',
                    display: 'flex',
                    flexDirection: 'column',
                    padding: 4,
                    zIndex: 1300
                }} elevation={4}>
                    <Typography variant="h6" gutterBottom sx={{ textAlign: 'center' }}>
                        Parámetros Utilizados
                    </Typography>
                    <Box sx={{
                        overflowY: 'auto',
                        maxHeight: '60vh',
                        marginTop: 2
                    }}>
                        <Table size="small" sx={{ width: '100%', tableLayout: 'fixed' }}>
                            <TableBody>
                                {Array.isArray(parametrosSeleccionados) ? (
                                    parametrosSeleccionados.map((parametro, index) => (
                                        <TableRow key={index}>
                                            <TableCell sx={{ fontWeight: 'bold', width: '250px', textAlign: 'right' }}>
                                                {parametro.nombreParametro}:
                                            </TableCell>
                                            <TableCell sx={{ textAlign: 'left' }}>
                                                {parametro.valor.replace(/,/g, ' - ')}
                                            </TableCell>
                                        </TableRow>
                                    ))
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={2}>
                                            No hay datos disponibles.
                                        </TableCell>
                                    </TableRow> 
                                )}
                            </TableBody>
                        </Table>
                    </Box>

                    {/* Botón fijo */}
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: 2 }}>
                        <Button variant="contained" onClick={() => setModalParametrosOpen(false)}>
                            Cerrar
                        </Button>
                    </Box>
                </Paper>
            )}
        </>
            
    )
}

export default ListHistoryPreApprobedClients