import { ESearchServices } from "@/constants/utils";
import { OptionListGridItem } from "@/interfaces/_common";
import { searchServices } from "@/services/searchServices";
import { useQuery } from "@tanstack/react-query";

interface UseQueryAutoCompleteDataGridProps {
    searchService: ESearchServices;
    isOpen: boolean;
    staleTime?: number;
}
const useQueryAutoCompleteDataGrid = ({ searchService, isOpen, staleTime }: UseQueryAutoCompleteDataGridProps) => {
    const {
        isPending,
        isLoading,
        isFetched,
        error,
        data,
        refetch: fetchConsult,
        isRefetching,
        isRefetchError,
    } = useQuery({
        queryKey: ['queryAutoCompleteDataGrid', searchService],
        queryFn: async () => {
            let data: OptionListGridItem[] = [];
            switch (searchService) {
                case ESearchServices.BRANCH:
                    const resp = await searchServices.branches();
                    data = resp?.items as OptionListGridItem[] ?? [];
                    break;
                default:
                    break;
            }
            return {
                data,
                error: null
            };
        },
        enabled: isOpen ?? false,
        retry: false,
        refetchOnMount: true,
        staleTime: staleTime ?? 15000,
    });

    return {
        isPending,
        isLoading,
        isFetched,
        error,
        data,
        fetchConsult,
        isRefetching,
        isRefetchError,
    };
}


export {
    useQueryAutoCompleteDataGrid
};