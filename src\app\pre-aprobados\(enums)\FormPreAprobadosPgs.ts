export enum FORM_GROUP_TABS_OBJ {
    ID = '1',
    LABEL = 'Pago de salarios'
}

export enum REVALIDAR_INTERNO {
    REVALIDAR_PENDIENTE = 'REVA<PERSON><PERSON><PERSON>_PENDIENTE',
    OK = 'OK',
    ERROR_PROCESO_REVALIDACION = 'ERROR_PROCESO_REVALIDACION',
    REVALIDAR_PARAM = 'SI'
}

export enum TIPO_FILTRO_LISTA_PREAPROBADOS {
    IGNORAR_FILTRO_MONTO = 'IGNORAR_FILTRO_MONTO',
    FILTRAR_MONTO = 'FILTRAR_MONTO'
}

export enum COLUMNA_FORMATEO_EXCEL {
    CUENTA = 'cuenta',
    ESTADO_INFORMCONF = 'estadoInformconf',
    ESTADO_BCP = 'estadoBCP',
    SUCURSAL = 'sucursal',
    REVALIDADO = 'revalidado',
    ELIMINADO_REVALIDACION = 'eliminadoPorRevalidacion'
}