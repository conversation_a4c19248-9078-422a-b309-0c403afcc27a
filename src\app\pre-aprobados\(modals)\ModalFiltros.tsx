import React, { FC, forwardRef } from 'react';
import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Autocomplete,
    Box,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Divider,
    FormControl,
    IconButton,
    InputLabel,
    MenuItem,
    Select,
    TextField
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SearchIcon from '@mui/icons-material/Search';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import { NumericFormat, NumericFormatProps } from 'react-number-format';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';
import { TIPO_FILTRO_LISTA_PREAPROBADOS } from '../(enums)/FormPreAprobadosPgs';
import { ORIGEN_CONSULTA } from '../(enums)/UtilEnum';
import 'dayjs/locale/es';
import { utilHelper } from '@/helpers/utilHelper';

interface CustomProps {
    onChange: (event: { target: { name: string; value: string } }) => void;
    name: string;
}

interface ModalFiltrosProps {
    open: boolean;
    onClose: () => void;
    onSearch: () => void;
    onFilterChange: (tipoFiltro: string, name: string, value: string | number) => void;
    filters: any;
    resetFilters: () => void;
    sucursalesData: any;
    origenConsultaFiltro: string;
    onSearchResetAll: () => void;
}

const NumericFormatCustom = forwardRef<NumericFormatProps, CustomProps>(
    function NumericFormatCustom(props, ref) {
        const { onChange, ...other } = props;

        return (
            <NumericFormat
                {...other}
                getInputRef={ref}
                onValueChange={(values) => {
                    onChange({
                        target: {
                            name: props.name,
                            value: values.value,
                        },
                    });
                }}
                thousandSeparator
                valueIsNumericString
            />
        );
    },
);

const ModalFiltros: FC<ModalFiltrosProps> = ({
    open,
    onClose,
    onSearch,
    onFilterChange,
    filters,
    resetFilters,
    sucursalesData,
    origenConsultaFiltro,
    onSearchResetAll
}) => {
    dayjs.locale('es');
    const handleDateChange = (origenConsulta: string) => (newValue: Dayjs | null) => {
        if(origenConsulta == ORIGEN_CONSULTA.LISTADO){
            if (newValue) {
                onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, 'fechaRevalidacion', newValue.format('DD/MM/YYYY HH:mm:ss'));
            } else {
                onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, 'fechaRevalidacion', '');
            }
        } else if (origenConsulta == ORIGEN_CONSULTA.HISTORICO){
            if (newValue) {
                const fechaFormateada = newValue.format('YYYYMM');
                onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, 'periodo', fechaFormateada);
            } else {
                onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, 'periodo', utilHelper.calculatePeriodo());
            }
        }
    };

    return (
        <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
            <DialogTitle
                sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    fontWeight: 'bold',
                }}
            >
                FILTROS DE BUSQUEDA
                <IconButton onClick={onClose}>
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogActions sx={{ justifyContent: 'center' }}>
                <Button onClick={onSearchResetAll} color="primary" variant="outlined" startIcon={<RestartAltIcon />}>
                    RECONSULTAR SIN FILTROS
                </Button>
                
                <Button
                    onClick={onSearch}
                    color="primary"
                    variant="contained"
                    startIcon={<SearchIcon />}
                >
                    Buscar
                </Button>
            </DialogActions>
            <Divider variant="middle" sx={{ marginTop: 2 }} />
            <DialogContent sx={{ paddingX: 4 }}>
                <Box sx={{ display: 'flex', flexWrap: 'wrap' }}>
                    <Accordion sx={{ width: '100%', boxShadow: 'rgba(0, 0, 0, 0.08) 0px 4px 12px;' }} defaultExpanded>
                        <AccordionSummary
                            expandIcon={<ExpandMoreIcon />}
                            aria-controls="panel1-content"
                            id="panel1-header"
                        >
                            <Divider variant="middle" sx={{ margin: 2, width: '95%', color: '#1D428A' }}>
                                FILTROS BASICOS
                            </Divider>
                        </AccordionSummary>
                        <AccordionDetails>
                            <Box sx={{ display: 'flex', gap: 3, width: '100%' }}>
                                <TextField
                                    label="Código del Cliente"
                                    name="codigoCliente"
                                    fullWidth
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                                <TextField
                                    label="Cuenta"
                                    name="cuenta"
                                    fullWidth
                                    value={filters.cuenta}
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                            </Box>
                            {origenConsultaFiltro == ORIGEN_CONSULTA.LISTADO ? (
                                <Box sx={{ display: 'flex', gap: 2, width: '100%', marginTop: 2 }}>
                                    <FormControl fullWidth>
                                        <InputLabel>Fue Revalidado?</InputLabel>
                                        <Select
                                            name="revalidado"
                                            value={filters.revalidado}
                                            onChange={(e: any) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                        >
                                            <MenuItem value="">Todos</MenuItem>
                                            <MenuItem value="SI">SI</MenuItem>
                                            <MenuItem value="NO">NO</MenuItem>
                                        </Select>
                                    </FormControl>
                                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                                        <DatePicker
                                            sx={{ width: '100%' }}
                                            label="Fecha Revalidación"
                                            value={filters.fechaRevalidacion ? dayjs(filters.fechaRevalidacion, 'DD/MM/YYYY') : null}
                                            onChange={handleDateChange(ORIGEN_CONSULTA.LISTADO)}
                                            slotProps={{ field: { clearable: true } }}
                                        />
                                    </LocalizationProvider>
                                </Box>
                            ) : (
                                    <Box sx={{ display: 'flex', gap: 2, width: '100%', marginTop: 2 }}>
                                        <FormControl fullWidth>
                                            <InputLabel>Fue eliminado por revalidacion?</InputLabel>
                                            <Select
                                                name="eliminadoPorRevalidacion"
                                                value={filters.eliminadoPorRevalidacion}
                                                onChange={(e: any) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                            >
                                                <MenuItem value="">Todos</MenuItem>
                                                <MenuItem value="SI">SI</MenuItem>
                                                <MenuItem value="NO">NO</MenuItem>
                                            </Select>
                                        </FormControl>
                                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                                            <DatePicker
                                                sx={{ width: '100%' }}
                                                label="Periodo"
                                                value={filters.periodo ? dayjs(filters.periodo, 'YYYYMM') : null}
                                                onChange={handleDateChange(ORIGEN_CONSULTA.HISTORICO)}
                                                views={['month', 'year']}
                                                openTo="month"
                                                disableFuture
                                                monthsPerRow={4}
                                                slotProps={{ field: { clearable: true } }}
                                            />
                                        </LocalizationProvider>
                                    </Box>
                            )}
                            
                            <Box sx={{ display: 'flex', gap: 2, width: '100%', marginTop: 2 }}>
                                <TextField
                                    label="Campaña"
                                    name="nombreCampanha"
                                    fullWidth
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                                <FormControl fullWidth>
                                    <InputLabel>Codeudor</InputLabel>
                                    <Select
                                        name="codeudor"
                                        value={filters.codeudor}
                                        onChange={(e: any) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                    >
                                        <MenuItem value="">Todos</MenuItem>
                                        <MenuItem value="SI">SI</MenuItem>
                                        <MenuItem value="NO">NO</MenuItem>
                                    </Select>
                                </FormControl>
                                <FormControl fullWidth>
                                    <Autocomplete
                                        options={sucursalesData}
                                        getOptionLabel={(option: any) => option.descripcion}
                                        renderInput={(params) => (
                                            <TextField
                                                {...params}
                                                label="Sucursal"
                                                variant="outlined"
                                            />
                                        )}
                                        onChange={(event, value) => {
                                            onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, 'sucursal', value ? value.codigo : '');
                                        }}
                                        renderOption={(props, option) => (
                                            <li {...props} key={option.codigo.toString()}>
                                                {option.descripcion}
                                            </li>
                                        )}
                                    />
                                </FormControl>
                            </Box>
                        </AccordionDetails>
                    </Accordion>

                    <Accordion sx={{ width: '100%', boxShadow: 'rgba(0, 0, 0, 0.08) 0px 4px 12px;' }}>
                        <AccordionSummary
                            expandIcon={<ExpandMoreIcon />}
                            aria-controls="panel2-content"
                            id="panel2-header"
                        >
                            <Divider variant="middle" sx={{ margin: 2, width: '95%', color: '#1D428A' }}>
                                CALIFICACIONES
                            </Divider>
                        </AccordionSummary>
                        <AccordionDetails>

                            <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
                                <TextField
                                    label="Calificación BCP"
                                    name="calificacionBCP"
                                    fullWidth
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                                <FormControl fullWidth>
                                    <InputLabel>Estado BCP</InputLabel>
                                    <Select
                                        name="estadoBCP"
                                        value={filters.estadoBCP}
                                        onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                    >
                                        <MenuItem value="">Todos</MenuItem>
                                        <MenuItem value="PENDIENTE">PENDIENTE</MenuItem>
                                        <MenuItem value="LISTO">LISTO</MenuItem>
                                    </Select>
                                </FormControl>
                                <TextField
                                    label="Calificación Conti"
                                    name="calificacionConti"
                                    fullWidth
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                            </Box>
                            <Box sx={{ display: 'flex', gap: 2, width: '100%', marginTop: 2 }}>
                                <FormControl fullWidth>
                                    <InputLabel>Estado de servicio externo</InputLabel>
                                    <Select
                                        name="estadoInformconf"
                                        value={filters.estadoInformconf}
                                        onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                    >
                                        <MenuItem value="">Todos</MenuItem>
                                        <MenuItem value="PENDIENTE">PENDIENTE</MenuItem>
                                        <MenuItem value="LISTO">LISTO</MenuItem>
                                    </Select>
                                </FormControl>

                                <TextField
                                    label="Calificación del servicio externo"
                                    name="inforcomf"
                                    fullWidth
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                                <TextField
                                    label="Lista Negativa (ALD)"
                                    name="ald"
                                    fullWidth
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                            </Box>
                        </AccordionDetails>
                    </Accordion>

                    <Accordion sx={{ width: '100%', boxShadow: 'rgba(0, 0, 0, 0.08) 0px 4px 12px;' }}>
                        <AccordionSummary
                            expandIcon={<ExpandMoreIcon />}
                            aria-controls="panel3-content"
                            id="panel3-header"
                        >
                            <Divider variant="middle" sx={{ margin: 2, width: '95%', color: '#1D428A' }}>
                                RANGO DE VALORES
                            </Divider>
                        </AccordionSummary>
                        <AccordionDetails>
                            <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
                                <TextField
                                    label="Promedio de acreditación Mínimo"
                                    name="promedioAcreditacionMin"
                                    value={filters.promedioAcreditacionMin}
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.FILTRAR_MONTO, e.target.name, e.target.value)}
                                    InputProps={{
                                        inputComponent: NumericFormatCustom as any,
                                    }}
                                    variant="standard"
                                    fullWidth
                                />
                                <TextField
                                    label="Promedio de acreditación Máximo"
                                    name="promedioAcreditacionMax"
                                    value={filters.promedioAcreditacionMax}
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.FILTRAR_MONTO, e.target.name, e.target.value)}
                                    InputProps={{
                                        inputComponent: NumericFormatCustom as any,
                                    }}
                                    variant="standard"
                                    fullWidth
                                />
                            </Box>
                            <Box sx={{ display: 'flex', gap: 2, width: '100%', marginTop: 2 }}>
                                <TextField
                                    label="Monto de prestamo Mínimo"
                                    name="montoPrestamoMin"
                                    value={filters.montoPrestamoMin}
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.FILTRAR_MONTO, e.target.name, e.target.value)}
                                    InputProps={{
                                        inputComponent: NumericFormatCustom as any,
                                    }}
                                    variant="standard"
                                    fullWidth
                                />
                                <TextField
                                    label="Monto de prestamo Máximo"
                                    name="montoPrestamoMax"
                                    value={filters.montoPrestamoMax}
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.FILTRAR_MONTO, e.target.name, e.target.value)}
                                    InputProps={{
                                        inputComponent: NumericFormatCustom as any,
                                    }}
                                    variant="standard"
                                    fullWidth
                                />
                            </Box>
                            <Box sx={{ display: 'flex', gap: 2, width: '100%', marginTop: 2 }}>
                                <TextField
                                    label="% de Deudas Mínimo"
                                    name="porcentajeEndeudamientoMin"
                                    type="number"
                                    fullWidth
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                                <TextField
                                    label="% de Deudas Máximo"
                                    name="porcentajeEndeudamientoMax"
                                    type="number"
                                    fullWidth
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                            </Box>
                            <Box sx={{ display: 'flex', gap: 2, width: '100%', marginTop: 2 }}>
                                <TextField
                                    label="Cantidad del Préstamo"
                                    name="cantidadPrestamo"
                                    type="number"
                                    fullWidth
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                                <TextField
                                    label="Referencia de atraso"
                                    name="referenciaAtraso"
                                    fullWidth
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                            </Box>
                            <Box sx={{ display: 'flex', gap: 2, width: '100%', marginTop: 2 }}>
                                <TextField
                                    label="Edad Mínima"
                                    name="edadMin"
                                    type="number"
                                    fullWidth
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                                <TextField
                                    label="Edad Máxima"
                                    name="edadMax"
                                    type="number"
                                    fullWidth
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                            </Box>
                            <Box sx={{ display: 'flex', gap: 2, width: '100%', marginTop: 2 }}>
                                <TextField
                                    label="Antiguedad Mínima"
                                    name="antiguedadMin"
                                    type="number"
                                    fullWidth
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                                <TextField
                                    label="Antiguedad Máxima"
                                    name="antiguedadMax"
                                    type="number"
                                    fullWidth
                                    onChange={(e) => onFilterChange(TIPO_FILTRO_LISTA_PREAPROBADOS.IGNORAR_FILTRO_MONTO, e.target.name, e.target.value)}
                                />
                            </Box>
                        </AccordionDetails>
                    </Accordion>
                </Box>
            </DialogContent>
        </Dialog>
    );
};

export default ModalFiltros;